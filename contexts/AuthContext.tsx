
import React, { createContext, useState, useCallback, useEffect, ReactNode } from 'react';
import { apiService } from '../services/apiService';

interface AuthContextType {
    isAuthenticated: boolean;
    isInitializing: boolean;
    login: (username: string, password: string) => Promise<void>;
    logout: () => void;
    user?: { id: number; username: string };
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
    const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
    const [isInitializing, setIsInitializing] = useState<boolean>(true);
    const [user, setUser] = useState<{ id: number; username: string } | undefined>(undefined);

    useEffect(() => {
        const validateExistingSession = async () => {
            setIsInitializing(true);
            
            // Set a timeout to prevent indefinite loading
            const timeoutId = setTimeout(() => {
                console.warn('Session validation timed out');
                setIsAuthenticated(false);
                setUser(undefined);
                setIsInitializing(false);
            }, 10000); // 10 second timeout
            
            try {
                const isValid = await apiService.validateSession();
                clearTimeout(timeoutId); // Clear timeout on success
                
                if (isValid) {
                    setIsAuthenticated(true);
                    // User info would be returned from session validation in a real app
                    // For now, we'll set a default user
                    setUser({ id: 1, username: 'user' });
                } else {
                    setIsAuthenticated(false);
                    setUser(undefined);
                }
            } catch (error) {
                clearTimeout(timeoutId); // Clear timeout on error
                console.error('Session validation failed:', error);
                setIsAuthenticated(false);
                setUser(undefined);
            } finally {
                setIsInitializing(false);
            }
        };

        validateExistingSession();
    }, []);
    
    const login = useCallback(async (username: string, password: string): Promise<void> => {
        try {
            const response = await apiService.login(username, password);
            setIsAuthenticated(true);
            setUser(response.user);
        } catch (error) {
            setIsAuthenticated(false);
            setUser(undefined);
            throw error;
        }
    }, []);

    const logout = useCallback(async () => {
        try {
            await apiService.logout();
        } catch (error) {
            // Continue with logout even if API call fails
        } finally {
            setIsAuthenticated(false);
            setUser(undefined);
        }
    }, []);

    const value = { isAuthenticated, isInitializing, login, logout, user };

    return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
