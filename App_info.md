Augment just finished syncing with your codebase! Here is a summary of what we saw:

LifeTracker is a personal task management React application built with TypeScript and Vite. Here's a brief overview:

Core Features:

Task management with priorities, due dates, categories, and recurring tasks
Calendar integration for date-based task filtering
Weather widget showing current conditions and 4-day forecast
Search and sorting functionality
Local data persistence using SQLite (sql.js)
Tech Stack:

React 19 with TypeScript
Vite for build tooling
TailwindCSS for styling
sql.js for client-side SQLite database
Open-Meteo API for weather data
Architecture:

Context-based state management (`TaskContext`, `AuthContext`, `SettingsContext`)
Service layer for database operations (`dbService`)
Component-based UI with main views: Tasks, Settings, Profile
Simple authentication (hardcoded credentials: `scharway`/`Lookup88?`)
Key Components:

Main layout with sidebar navigation
Task list with filtering and search
Calendar widget for date selection
Weather widget forLifeTracker is a personal task management React application built with TypeScript and Vite. Here's a brief overview:
Core Features:

Task management with priorities, due dates, categories, and recurring tasks
Calendar integration for date-based task filtering
Weather widget showing current conditions and 4-day forecast
Search and sorting functionality
Local data persistence using SQLite (sql.js)
Tech Stack:

React 19 with TypeScript
Vite for build tooling
TailwindCSS for styling
sql.js for client-side SQLite database
Open-Meteo API for weather data
Architecture:

Context-based state management (`TaskContext`, `AuthContext`, `SettingsContext`)
Service layer for database operations (`dbService`)
Component-based UI with main views: Tasks, Settings, Profile
Simple authentication (hardcoded credentials: `scharway`/`Lookup88?`)
Key Components:

Main layout with sidebar navigation
Task list with filtering and search
Calendar widget for date selection
Weather widget for
Drop files to attach as context
images (png, jpg, jpeg)


Letf to do:

Clean up this React/TypeScript application for production deployment by completing the following tasks in order:

**Phase 1: Code Cleanup**
1. Remove all mock data, placeholder content, and unused/dead code from the entire codebase
2. Remove all references to "gemini" (API calls, imports, configurations, comments, etc.) from files like `vite.config.ts` and any other locations
3. Identify and eliminate any development-only code or debugging artifacts (console.logs, test data, etc.)

**Phase 2: Backend Implementation**
1. Create an Express.js backend server to replace the current client-side SQLite implementation
2. Implement server-side SQLite database with the following requirements:
   - Design for 30-year operational lifespan with appliance-grade reliability
   - Implement proper database schema with appropriate indexes for performance
   - Store database file on server filesystem (no browser-based storage)
   - Use proper SQLite libraries for Node.js (e.g., better-sqlite3)

**Phase 3: Authentication & Security**
1. Implement secure authentication system:
   - Hash the hardcoded credentials (`scharway`/`Lookup88?`) using bcrypt or similar
   - Store hashed credentials in the database during initialization
   - Implement JWT or session-based authentication
   - Add automatic logout after 5 minutes of inactivity
   - Clear authentication state when browser/tab is closed
   - Ensure login page cannot be bypassed via direct URL access
2. Implement comprehensive security measures:
   - Input validation on all endpoints
   - SQL injection prevention using parameterized queries
   - CORS configuration
   - Rate limiting on authentication endpoints

**Phase 4: Data Management**
1. Ensure permanent data deletion when users delete records (no soft deletes)
2. Implement SQLite VACUUM operations to reclaim disk space after deletions
3. Set up automatic database maintenance (VACUUM, ANALYZE) on a scheduled basis
4. Create proper API endpoints for all CRUD operations

**Phase 5: Production Readiness**
1. Add comprehensive error handling throughout the application
2. Remove all logging (as specified - no logging in production)
3. Configure proper production build settings
4. No backup implementation required (as specified)

**Critical Constraints:**
- **DO NOT** modify the UI/appearance in any way - preserve all existing styling, components, and visual elements
- **DO NOT** change any existing functionality or user workflows
- **DO NOT** introduce any new features beyond what's specified
- **DO NOT** modify the authentication credentials or add user registration
- Make minimal changes to achieve production readiness
- Maintain SQLite as the database solution
- This is a single-user application with no signup functionality

**Implementation Requirements:**
1. Create a detailed implementation plan before starting
2. Use sequential thinking to break down the work systematically
3. Ensure the frontend continues to work with the new backend API
4. Maintain all existing React contexts and state management
5. Preserve the current task management, settings, and profile functionality
6. Keep the existing database schema and data structures intact

**Technical Stack Constraints:**
- Backend: Express.js with SQLite
- Frontend: Keep existing React/TypeScript setup
- Authentication: Server-side with automatic timeout
- Database: Server-side SQLite file storage

Before implementation, analyze the current codebase structure (particularly `services/dbService.ts`, `contexts/AuthContext.tsx`, and the overall application architecture) and create a detailed step-by-step plan that ensures nothing is missed while preserving the existing user experience.


Clean up this React/TypeScript application for production deployment by completing the following tasks in order:

**Phase 1: Code Cleanup**
1. Remove all mock data, placeholder content, and unused/dead code from the entire codebase
2. Remove all references to "gemini" (API calls, imports, configurations, comments, etc.) from files like `vite.config.ts` and any other locations
3. Identify and eliminate any development-only code or debugging artifacts (console.logs, test data, etc.)

**Phase 2: Backend Implementation**
1. Create an Express.js backend server to replace the current client-side SQLite implementation
2. Implement server-side SQLite database with the following requirements:
   - Design for 30-year operational lifespan with appliance-grade reliability
   - Implement proper database schema with appropriate indexes for performance
   - Store database file on server filesystem (no browser-based storage)
   - Use proper SQLite libraries for Node.js (e.g., better-sqlite3)

**Phase 3: Authentication & Security**
1. Implement secure authentication system:
   - Hash the hardcoded credentials (`scharway`/`Lookup88?`) using bcrypt or similar
   - Store hashed credentials in the database during initialization
   - Implement JWT or session-based authentication
   - Add automatic logout after 5 minutes of inactivity
   - Clear authentication state when browser/tab is closed
   - Ensure login page cannot be bypassed via direct URL access
2. Implement comprehensive security measures:
   - Input validation on all endpoints
   - SQL injection prevention using parameterized queries
   - CORS configuration
   - Rate limiting on authentication endpoints

**Phase 4: Data Management**
1. Ensure permanent data deletion when users delete records (no soft deletes)
2. Implement SQLite VACUUM operations to reclaim disk space after deletions
3. Set up automatic database maintenance (VACUUM, ANALYZE) on a scheduled basis
4. Create proper API endpoints for all CRUD operations

**Phase 5: Production Readiness**
1. Add comprehensive error handling throughout the application
2. Remove all logging (as specified - no logging in production)
3. Configure proper production build settings
4. No backup implementation required (as specified)

**Critical Constraints:**
- **DO NOT** modify the UI/appearance in any way - preserve all existing styling, components, and visual elements
- **DO NOT** change any existing functionality or user workflows
- **DO NOT** introduce any new features beyond what's specified
- **DO NOT** modify the authentication credentials or add user registration
- Make minimal changes to achieve production readiness
- Maintain SQLite as the database solution
- This is a single-user application with no signup functionality

**Implementation Requirements:**
1. Create a detailed implementation plan before starting
2. Use sequential thinking to break down the work systematically
3. Ensure the frontend continues to work with the new backend API
4. Maintain all existing React contexts and state management
5. Preserve the current task management, settings, and profile functionality
6. Keep the existing database schema and data structures intact

**Technical Stack Constraints:**
- Backend: Express.js with SQLite
- Frontend: Keep existing React/TypeScript setup
- Authentication: Server-side with automatic timeout
- Database: Server-side SQLite file storage

Before implementation, analyze the current codebase structure (particularly `services/dbService.ts`, `contexts/AuthContext.tsx`, and the overall application architecture) and create a detailed step-by-step plan that ensures nothing is missed while preserving the existing user experience.

