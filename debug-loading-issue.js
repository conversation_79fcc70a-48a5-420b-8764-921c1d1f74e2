import { chromium } from 'playwright';
import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

async function recordLoadingIssue() {
  console.log('Starting Playwright recording of loading issue...');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 1000 // Slow down for better observation
  });
  
  const context = await browser.newContext({
    recordVideo: {
      dir: './videos/',
      size: { width: 1280, height: 720 }
    }
  });
  
  const page = await context.newPage();
  
  // Enable console logging
  page.on('console', msg => {
    console.log(`BROWSER: ${msg.type()}: ${msg.text()}`);
  });
  
  // Track network requests
  page.on('request', request => {
    console.log(`REQUEST: ${request.method()} ${request.url()}`);
  });
  
  page.on('response', response => {
    console.log(`RESPONSE: ${response.status()} ${response.url()}`);
  });
  
  // Track loading states
  let loadingStates = [];
  
  try {
    console.log('Navigating to app...');
    await page.goto('http://localhost:5173', { waitUntil: 'networkidle' });
    
    // Wait for initial load and record loading states
    console.log('Recording loading states...');
    
    // Check for loading spinner
    const loadingSpinner = page.locator('.animate-spin');
    const loadingText = page.locator('text=Loading application...');
    
    let iteration = 0;
    const maxIterations = 30; // 30 seconds max
    
    while (iteration < maxIterations) {
      const spinnerVisible = await loadingSpinner.isVisible().catch(() => false);
      const textVisible = await loadingText.isVisible().catch(() => false);
      
      loadingStates.push({
        time: Date.now(),
        iteration,
        spinnerVisible,
        textVisible,
        url: page.url()
      });
      
      console.log(`Iteration ${iteration}: Spinner=${spinnerVisible}, Text=${textVisible}`);
      
      // If neither loading indicator is visible, we might be done
      if (!spinnerVisible && !textVisible) {
        // Check if we're on login page or main app
        const loginForm = await page.locator('form').isVisible().catch(() => false);
        const mainLayout = await page.locator('[data-testid="main-layout"], .flex.h-screen').isVisible().catch(() => false);
        
        console.log(`Login form visible: ${loginForm}, Main layout visible: ${mainLayout}`);
        
        if (loginForm || mainLayout) {
          console.log('App appears to have loaded successfully');
          break;
        }
      }
      
      await page.waitForTimeout(1000);
      iteration++;
    }
    
    // Take a screenshot of final state
    await page.screenshot({ path: 'final-state.png', fullPage: true });
    
    // Try to interact with the app if it's loaded
    const loginForm = await page.locator('form').isVisible().catch(() => false);
    if (loginForm) {
      console.log('Login form detected, attempting login...');
      await page.fill('input#username', 'scharway');
      await page.fill('input#password', 'Lookup88?');
      await page.click('button[type="submit"]');
      
      // Wait for post-login loading
      await page.waitForTimeout(3000);
      
      // Check for task loading
      const taskLoadingSpinner = page.locator('text=Loading your tasks...');
      let taskLoadingIterations = 0;
      
      while (taskLoadingIterations < 10) {
        const taskSpinnerVisible = await taskLoadingSpinner.isVisible().catch(() => false);
        console.log(`Task loading iteration ${taskLoadingIterations}: Spinner=${taskSpinnerVisible}`);
        
        if (!taskSpinnerVisible) {
          break;
        }
        
        await page.waitForTimeout(1000);
        taskLoadingIterations++;
      }
      
      await page.screenshot({ path: 'post-login-state.png', fullPage: true });
    }
    
  } catch (error) {
    console.error('Error during recording:', error);
    await page.screenshot({ path: 'error-state.png', fullPage: true });
  }
  
  await context.close();
  await browser.close();
  
  // Save loading states to file
  fs.writeFileSync('loading-states.json', JSON.stringify(loadingStates, null, 2));
  
  console.log('Recording complete. Check videos/ directory for recording.');
  console.log('Loading states saved to loading-states.json');
  
  return loadingStates;
}

// Install playwright if needed
async function ensurePlaywright() {
  try {
    await import('playwright');
  } catch (error) {
    console.log('Installing Playwright...');
    execSync('npm install playwright', { stdio: 'inherit' });
    execSync('npx playwright install chromium', { stdio: 'inherit' });
  }
}

async function main() {
  try {
    await ensurePlaywright();
    const states = await recordLoadingIssue();
    
    // Analyze the loading states
    console.log('\n=== LOADING ANALYSIS ===');
    const longLoadingPeriods = states.filter(state => state.spinnerVisible || state.textVisible);
    
    if (longLoadingPeriods.length > 5) {
      console.log(`⚠️  Extended loading detected: ${longLoadingPeriods.length} iterations with loading indicators`);
    }
    
    console.log(`Total loading time: ~${states.length} seconds`);
    
  } catch (error) {
    console.error('Failed to run loading analysis:', error);
  }
}

// Run if this is the main module
main();
