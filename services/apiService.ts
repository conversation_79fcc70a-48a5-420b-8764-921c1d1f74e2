import { Task, Settings, Priority, RecurrenceType } from '../types';

// API configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

// Token management
const TOKEN_KEY = 'authToken';
const REFRESH_THRESHOLD = 60000; // 1 minute before expiration

interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    field?: string;
    details?: any;
  };
  metadata: {
    timestamp: string;
    requestId: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      hasNext: boolean;
    };
  };
}

interface AuthResponse {
  token: string;
  user: {
    id: number;
    username: string;
  };
  expiresAt: string;
}

class ApiService {
  private sessionTimeout: NodeJS.Timeout | null = null;
  private refreshTimeout: NodeJS.Timeout | null = null;
  private _initialized = false;
  private readonly maxRetries = 3;
  private readonly baseDelay = 1000; // 1 second

  constructor() {
    // Initialize session management on construction
    this.initializeSessionManagement();
  }

  private initializeSessionManagement() {
    if (this._initialized) return;
    
    // Set up automatic logout on tab/browser close
    window.addEventListener('beforeunload', () => {
      this.clearSession();
    });

    // Check for existing token and set up refresh
    const token = this.getToken();
    if (token) {
      this.setupTokenRefresh();
      this.resetInactivityTimer();
    }

    this._initialized = true;
  }

  private getToken(): string | null {
    return localStorage.getItem(TOKEN_KEY);
  }

  private setToken(token: string, expiresAt: string): void {
    localStorage.setItem(TOKEN_KEY, token);
    localStorage.setItem(`${TOKEN_KEY}_expires`, expiresAt);
    this.setupTokenRefresh();
    this.resetInactivityTimer();
  }

  private clearSession(): void {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(`${TOKEN_KEY}_expires`);
    if (this.sessionTimeout) {
      clearTimeout(this.sessionTimeout);
      this.sessionTimeout = null;
    }
    if (this.refreshTimeout) {
      clearTimeout(this.refreshTimeout);
      this.refreshTimeout = null;
    }
  }

  private setupTokenRefresh(): void {
    const expiresAt = localStorage.getItem(`${TOKEN_KEY}_expires`);
    if (!expiresAt) return;

    const expiryTime = new Date(expiresAt).getTime();
    const currentTime = Date.now();
    const timeUntilRefresh = expiryTime - currentTime - REFRESH_THRESHOLD;

    if (timeUntilRefresh > 0) {
      this.refreshTimeout = setTimeout(() => {
        this.refreshToken();
      }, timeUntilRefresh);
    }
  }

  private resetInactivityTimer(): void {
    if (this.sessionTimeout) {
      clearTimeout(this.sessionTimeout);
    }

    // Auto logout after 5 minutes of inactivity
    this.sessionTimeout = setTimeout(() => {
      this.logout();
      window.location.href = '/';
    }, 300000); // 5 minutes
  }

  private async refreshToken(): Promise<void> {
    try {
      const response = await this.apiCall<AuthResponse>('/auth/refresh', {
        method: 'POST'
      });
      
      if (response.success && response.data) {
        this.setToken(response.data.token, response.data.expiresAt);
      }
    } catch (error) {
      // If refresh fails, logout user
      this.logout();
      window.location.href = '/';
    }
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async apiCall<T>(endpoint: string, options: RequestInit = {}): Promise<APIResponse<T>> {
    return this.apiCallWithRetry<T>(endpoint, options, 0);
  }

  private async apiCallWithRetry<T>(endpoint: string, options: RequestInit = {}, retryCount: number): Promise<APIResponse<T>> {
    const token = this.getToken();
    const url = `${API_BASE_URL}${endpoint}`;

    const config: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': `Bearer ${token}` }),
        ...options.headers,
      },
    };

    // Reset inactivity timer on any API call
    if (token) {
      this.resetInactivityTimer();
    }

    try {
      const response = await fetch(url, config);
      
      // Handle 429 (Too Many Requests) with exponential backoff
      if (response.status === 429 && retryCount < this.maxRetries) {
        const delay = this.baseDelay * Math.pow(2, retryCount);
        console.warn(`Rate limited (429). Retrying in ${delay}ms... (attempt ${retryCount + 1}/${this.maxRetries})`);
        await this.sleep(delay);
        return this.apiCallWithRetry<T>(endpoint, options, retryCount + 1);
      }

      const result: APIResponse<T> = await response.json();

      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid, clear session and redirect
          this.clearSession();
          window.location.href = '/';
          throw new Error('Authentication required');
        }
        if (response.status === 429) {
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        throw new Error(result.error?.message || 'API request failed');
      }

      return result;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Network error occurred');
    }
  }

  // Authentication methods
  async login(username: string, password: string): Promise<{ user: { id: number; username: string } }> {
    const response = await this.apiCall<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ username, password })
    });

    if (response.success && response.data) {
      this.setToken(response.data.token, response.data.expiresAt);
      return { user: response.data.user };
    }

    throw new Error('Login failed');
  }

  async logout(): Promise<void> {
    try {
      await this.apiCall('/auth/logout', { method: 'POST' });
    } catch (error) {
      // Continue with logout even if API call fails
    } finally {
      this.clearSession();
    }
  }

  async validateSession(): Promise<boolean> {
    try {
      const response = await this.apiCall<{ user: any; isValid: boolean }>('/auth/session');
      return response.success && response.data?.isValid === true;
    } catch (error) {
      return false;
    }
  }

  // Task management methods
  async getTasks(): Promise<Task[]> {
    const response = await this.apiCall<Task[]>('/tasks');
    return response.data || [];
  }

  async addTask(task: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>): Promise<Task> {
    const response = await this.apiCall<Task>('/tasks', {
      method: 'POST',
      body: JSON.stringify(task)
    });

    if (response.success && response.data) {
      return response.data;
    }

    throw new Error('Failed to create task');
  }

  async addFullTask(task: Task): Promise<void> {
    await this.apiCall<Task>('/tasks', {
      method: 'POST',
      body: JSON.stringify(task)
    });
  }

  async updateTask(task: Task): Promise<void> {
    await this.apiCall<Task>(`/tasks/${task.id}`, {
      method: 'PUT',
      body: JSON.stringify(task)
    });
  }

  async deleteTask(taskId: string): Promise<void> {
    await this.apiCall(`/tasks/${taskId}`, {
      method: 'DELETE'
    });
  }

  async seedInitialTasks(tasksToSeed: Omit<Task, 'id' | 'createdAt' | 'isCompleted' | 'completedAt'>[]): Promise<void> {
    try {
      await this.apiCall('/tasks/seed', {
        method: 'POST',
        body: JSON.stringify({ tasks: tasksToSeed })
      });
    } catch (error) {
      // If seeding fails because already seeded, that's okay
      if (error instanceof Error && !error.message.includes('already seeded')) {
        throw error;
      }
    }
  }

  // Settings management methods
  async getSettings(): Promise<Settings> {
    const response = await this.apiCall<Settings>('/settings');
    return response.data || { showCompleted: true, seeded: false };
  }

  async updateSetting(key: keyof Settings, value: string | boolean): Promise<void> {
    await this.apiCall(`/settings/${key}`, {
      method: 'PUT',
      body: JSON.stringify({ value })
    });
  }

  // Transaction support (for compatibility with existing code)
  async runInTransaction(callback: () => Promise<void>): Promise<void> {
    // Since we're using API calls, we don't have direct transaction support
    // Instead, we'll execute the callback and handle errors appropriately
    try {
      await callback();
    } catch (error) {
      // In case of error, the backend should handle rollback
      throw error;
    }
  }

  // Compatibility methods to match dbService interface
  initialize(): Promise<void> {
    // API service doesn't need initialization like database
    return Promise.resolve();
  }

  get isInitialized(): boolean {
    return true; // API service is always "initialized"
  }

  clearDatabase(): void {
    // For API service, this just clears the session
    this.clearSession();
  }

  get _inTransaction(): boolean {
    return false; // API service doesn't track transaction state
  }
}

// Export singleton instance
export const apiService = new ApiService();