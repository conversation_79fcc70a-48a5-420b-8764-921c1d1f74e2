import { WeatherData } from '../types';

const API_BASE_URL = 'https://api.open-meteo.com/v1/forecast';

// Default coordinates (can be overridden via environment variables or user settings)
const DEFAULT_COORDS = {
    lat: Number(import.meta.env.VITE_DEFAULT_LAT) || 38.6270,
    lon: Number(import.meta.env.VITE_DEFAULT_LON) || -90.1994,
    name: import.meta.env.VITE_DEFAULT_LOCATION || 'Default Location'
};

export const fetchWeather = async (coordinates?: { lat: number; lon: number; name: string }): Promise<WeatherData> => {
    const { lat, lon, name } = coordinates || DEFAULT_COORDS;

    const params = new URLSearchParams({
        latitude: lat.toString(),
        longitude: lon.toString(),
        current: 'temperature_2m,apparent_temperature,weather_code',
        daily: 'weather_code,temperature_2m_max,temperature_2m_min,precipitation_sum',
        temperature_unit: 'fahrenheit',
        wind_speed_unit: 'mph',
        precipitation_unit: 'inch',
        timezone: 'auto',
        forecast_days: '5'
    });

    const response = await fetch(`${API_BASE_URL}?${params.toString()}`);
    if (!response.ok) {
        throw new Error('Failed to fetch weather data from API.');
    }
    const data = await response.json();

    return {
        locationName: name,
        current: {
            temperature: Math.round(data.current.temperature_2m),
            weatherCode: data.current.weather_code,
            feelsLike: Math.round(data.current.apparent_temperature),
        },
        forecast: data.daily.time.slice(1, 5).map((date: string, index: number) => ({
            date: date,
            weatherCode: data.daily.weather_code[index + 1],
            tempHigh: Math.round(data.daily.temperature_2m_max[index + 1]),
            tempLow: Math.round(data.daily.temperature_2m_min[index + 1]),
            precipitation: data.daily.precipitation_sum[index + 1] > 0 ? data.daily.precipitation_sum[index + 1] : undefined,
        })),
    };
};

export const getWeatherDisplay = (code: number): { icon: string; description: string } => {
    const descriptions: { [key: number]: { icon: string; description: string } } = {
        0: { icon: '☀️', description: 'Clear sky' },
        1: { icon: '🌤️', description: 'Mainly clear' },
        2: { icon: '🌥️', description: 'Partly cloudy' },
        3: { icon: '☁️', description: 'Overcast' },
        45: { icon: '🌫️', description: 'Fog' },
        48: { icon: '🌫️', description: 'Depositing rime fog' },
        51: { icon: '🌦️', description: 'Light drizzle' },
        53: { icon: '🌦️', description: 'Moderate drizzle' },
        55: { icon: '🌦️', description: 'Dense drizzle' },
        56: { icon: '🧊', description: 'Light freezing drizzle' },
        57: { icon: '🧊', description: 'Dense freezing drizzle' },
        61: { icon: '🌧️', description: 'Slight rain' },
        63: { icon: '🌧️', description: 'Moderate rain' },
        65: { icon: '🌧️', description: 'Heavy rain' },
        66: { icon: '🧊', description: 'Light freezing rain' },
        67: { icon: '🧊', description: 'Heavy freezing rain' },
        71: { icon: '❄️', description: 'Slight snow fall' },
        73: { icon: '❄️', description: 'Moderate snow fall' },
        75: { icon: '❄️', description: 'Heavy snow fall' },
        77: { icon: '❄️', description: 'Snow grains' },
        80: { icon: '⛈️', description: 'Slight rain showers' },
        81: { icon: '⛈️', description: 'Moderate rain showers' },
        82: { icon: '⛈️', description: 'Violent rain showers' },
        85: { icon: '🌨️', description: 'Slight snow showers' },
        86: { icon: '🌨️', description: 'Heavy snow showers' },
        95: { icon: '🌩️', description: 'Thunderstorm' },
        96: { icon: '🌩️', description: 'Thunderstorm, slight hail' },
        99: { icon: '🌩️', description: 'Thunderstorm, heavy hail' },
    };
    return descriptions[code] || { icon: '🤷', description: 'Unknown' };
};
