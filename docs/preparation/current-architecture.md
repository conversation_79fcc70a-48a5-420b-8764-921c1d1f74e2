# LifeTracker Current Architecture Documentation

## Executive Summary

LifeTracker is a client-side personal task management application built with React 19 and TypeScript. The architecture follows modern React patterns with Context API for state management, a service layer for data operations, and component-based UI organization. The application uses SQLite for client-side data storage and integrates with external weather APIs for enhanced user experience.

This documentation serves as a foundation for understanding the current system before migrating to a backend-supported architecture.

## Technology Stack

### Core Framework
- **React**: 19.1.0 (Latest with concurrent features)
- **TypeScript**: 5.8.2 (Strict type checking enabled)
- **Build Tool**: Vite 6.2.0 (Fast development and optimized builds)
- **Node.js**: Required for development (version not specified in package.json)

### Data & Storage
- **Database**: SQLite via sql.js 1.13.0 (Client-side, in-memory with localStorage persistence)
- **Storage**: localStorage for database persistence and authentication state
- **External APIs**: Open-Meteo weather service (free, no API key required)

### Styling & UI
- **CSS Framework**: TailwindCSS (loaded via CDN in index.html)
- **Icons**: Custom SVG icons in components/icons/Icons.tsx
- **Layout**: Responsive design with mobile-first approach

## Application Architecture

### High-Level Structure
```
LifeTracker Application
├── Authentication Layer (AuthContext)
├── State Management Layer (React Contexts)
├── Service Layer (Database & External APIs)
├── Component Layer (UI Components)
└── Utility Layer (Date helpers, etc.)
```

### Directory Organization
```
/src
├── components/           # Reusable UI components
│   ├── auth/            # Authentication components
│   ├── icons/           # SVG icon components  
│   ├── layout/          # Layout and navigation
│   ├── tasks/           # Task management UI
│   └── ui/              # Generic UI components
├── contexts/            # React Context providers
├── hooks/               # Custom React hooks
├── services/            # Data and API services
├── utils/               # Utility functions
├── views/               # Main application views
└── types.ts             # TypeScript type definitions
```

## Data Layer Architecture

### Database Service (dbService.ts)

**Purpose**: Centralized SQLite operations with transaction support and localStorage persistence

**Key Components**:
- **Initialization**: Fetches sql.js WASM binary from CDN, initializes database
- **Schema Management**: Creates tables and default settings on first run
- **Transaction Support**: ACID compliance for complex operations
- **Persistence**: Automatic save to localStorage after operations

**Database Schema**:
```sql
-- Tasks table: Core task management
CREATE TABLE tasks (
    id TEXT PRIMARY KEY,           -- UUID generated by crypto.randomUUID()
    title TEXT NOT NULL,           -- Task title
    description TEXT,              -- Optional description
    dueDate TEXT NOT NULL,         -- YYYY-MM-DD format
    dueTime TEXT NOT NULL,         -- HH:mm format
    isCompleted INTEGER NOT NULL,  -- Boolean as integer
    completedAt TEXT,              -- ISO timestamp when completed
    priority TEXT NOT NULL,        -- Low/Medium/High enum
    category TEXT,                 -- Optional category
    isRecurring INTEGER NOT NULL,  -- Boolean for recurring tasks
    recurrenceType TEXT,           -- Daily/Weekly/Monthly/Yearly
    recurrenceInterval INTEGER,    -- Multiplier for recurrence
    createdAt TEXT NOT NULL        -- ISO timestamp
);

-- Settings table: Application configuration
CREATE TABLE settings (
    key TEXT PRIMARY KEY,          -- Setting name
    value TEXT NOT NULL            -- Setting value as string
);
```

**Critical Operations**:
- **Transaction Management**: Ensures data consistency for complex operations
- **Recurring Task Logic**: Creates completed instances while updating original task dates
- **Auto-Save**: Persists to localStorage after each operation
- **Error Handling**: Graceful degradation with console error logging

### Storage Strategy

**Client-Side Only**: All data stored in browser localStorage
- **Advantages**: No server dependency, offline functionality, instant synchronization
- **Disadvantages**: Data loss risk, no cross-device sync, storage limits

**Persistence Implementation**:
```typescript
// Database stored as Uint8Array in localStorage
const data = db.export();
localStorage.setItem(DB_STORAGE_KEY, JSON.stringify(Array.from(data)));

// Restoration from localStorage
const storedDb = localStorage.getItem(DB_STORAGE_KEY);
const dbArray = new Uint8Array(JSON.parse(storedDb));
db = new SQL.Database(dbArray);
```

## State Management Architecture

### Context Providers Hierarchy
```typescript
<AuthProvider>
  <SettingsProvider>
    <TaskProvider>
      <App />
    </TaskProvider>
  </SettingsProvider>
</AuthProvider>
```

### Authentication Context (AuthContext.tsx)

**Purpose**: Manages user authentication and session state

**State Management**:
- `isAuthenticated`: Boolean authentication status
- `isInitializing`: Loading state during app startup
- `login()`: Async authentication with hardcoded credentials
- `logout()`: Clears authentication and database

**Current Implementation Issues**:
- Hardcoded username/password (security vulnerability)
- No registration system
- Basic session management

**Authentication Flow**:
1. App initialization checks localStorage for existing session
2. Login validates against hardcoded credentials
3. Successful login sets authentication state and localStorage flag
4. Logout clears all data including database

### Task Context (TaskContext.tsx)

**Purpose**: Manages task CRUD operations and business logic

**State Management**:
- `tasks`: Array of all tasks
- `isSeeding`: Loading state during initial data seeding
- `addTask()`: Creates new tasks with auto-generated IDs
- `updateTask()`: Handles updates with special recurring task logic
- `deleteTask()`: Removes tasks from database

**Complex Logic - Recurring Tasks**:
When a recurring task is completed:
1. Database transaction begins
2. Original task date updated to next occurrence
3. New completed task instance created (non-recurring)
4. Transaction committed or rolled back on error

**Data Seeding**:
- Automatic seeding of initial tasks for new users
- Prevents re-seeding if tasks already exist
- Uses database transaction for consistency

### Settings Context (SettingsContext.tsx)

**Purpose**: Manages application settings and user preferences

**Current Settings**:
- `showCompleted`: Boolean for displaying completed tasks
- `seeded`: Boolean tracking if initial data was seeded

**Implementation**: Simple key-value store using database settings table

## Component Architecture

### Layout Components

**MainLayout.tsx**: Application shell with sidebar navigation
- Responsive design with mobile hamburger menu
- View switching between Tasks, Settings, Profile
- Global search functionality (placeholder implementation)

**Header.tsx**: Top navigation with user info and actions
- Date/time display widget
- User profile access
- Logout functionality

**Sidebar.tsx**: Navigation menu with view selection
- Tasks, Settings, Profile navigation
- Responsive collapse on mobile
- Visual feedback for active view

### Task Management Components

**TasksView.tsx**: Main task management interface
- Integrates TaskList and CalendarWidget
- View coordination between components
- Primary user interface

**TaskList.tsx**: Task display and interaction
- Filtering by date, category, completion status
- Search functionality
- Task completion toggling
- Integration with TaskFormModal for editing

**TaskCard.tsx**: Individual task display component
- Due date/time formatting with overdue detection
- Priority and category display
- Completion status toggle
- Edit and delete actions

**TaskFormModal.tsx**: Task creation and editing interface
- Form validation for required fields
- Recurring task configuration
- Priority and category selection
- Date/time picker integration

### Widget Components

**CalendarWidget.tsx**: Date selection interface
- Month/year navigation
- Date selection for task filtering
- Visual indication of selected date
- Responsive design

**WeatherWidget.tsx**: Weather information display
- Current conditions for hardcoded St. Louis location
- 4-day forecast display
- Error handling for API failures
- Loading states with skeleton UI

**DateTimeDisplay.tsx**: Real-time clock widget
- Live updating current time
- Formatted date display
- Used in header component

## Service Layer Architecture

### Database Service (dbService.ts)

**Initialization Strategy**:
```typescript
// Fetch WASM binary from CDN to avoid file system issues
const wasmResponse = await fetch('https://cdn.jsdelivr.net/npm/sql.js@1.13.0/dist/sql-wasm.wasm');
const wasmBinary = await wasmResponse.arrayBuffer();
SQL = await initSqlJs({ wasmBinary });
```

**Transaction Management**:
```typescript
async runInTransaction(callback: () => Promise<void>): Promise<void> {
    db.exec("BEGIN TRANSACTION;");
    try {
        await callback();
        db.exec("COMMIT;");
    } catch (e) {
        db.exec("ROLLBACK;");
        throw e;
    }
}
```

### Weather Service (weatherService.ts)

**API Integration**: Open-Meteo free weather service
- No API key required
- RESTful interface with URL parameters
- 5-day forecast data

**Current Limitations**:
- Hardcoded to St. Louis, MO coordinates
- No user location detection
- No error retry logic

**Data Transformation**:
- Raw API data converted to application-specific format
- Temperature rounding and unit conversion
- Weather code mapping to icons and descriptions

## Data Flow Architecture

### Task Management Flow
```
User Action (TaskCard/TaskFormModal)
    ↓
TaskContext.updateTask()
    ↓
dbService.runInTransaction()
    ↓
Database Update (with recurring logic)
    ↓
localStorage.setItem()
    ↓
TaskContext.refreshTasks()
    ↓
Component Re-render
```

### Authentication Flow
```
LoginPage.handleSubmit()
    ↓
AuthContext.login()
    ↓
Credential Validation (hardcoded)
    ↓
localStorage.setItem('isAuthenticated')
    ↓
App.tsx re-renders with authenticated state
    ↓
MainLayout renders with full application
```

### Application Initialization Flow
```
App.tsx useEffect()
    ↓
AuthContext checks localStorage
    ↓
dbService.initialize()
    ↓
TaskContext.loadAndSeedTasks()
    ↓
Initial render with data
```

## Type System Architecture

### Core Domain Types (types.ts)

**Task Interface**: Central data model
```typescript
interface Task {
    id: string;                    // UUID
    title: string;                 // Required
    description?: string;          // Optional
    dueDate: string;              // YYYY-MM-DD
    dueTime: string;              // HH:mm
    isCompleted: boolean;
    completedAt?: string;         // ISO timestamp
    priority: Priority;           // Enum
    category?: string;            // Optional
    isRecurring: boolean;
    recurrenceType?: RecurrenceType; // Enum
    recurrenceInterval?: number;
    createdAt: string;            // ISO timestamp
}
```

**Enums for Constraints**:
```typescript
enum Priority { Low = 'Low', Medium = 'Medium', High = 'High' }
enum RecurrenceType { Daily = 'Daily', Weekly = 'Weekly', Monthly = 'Monthly', Yearly = 'Yearly' }
```

**Context Type Definitions**: Strongly typed context interfaces for all providers

## Security Architecture

### Current Security Model

**Authentication**: Basic username/password with localStorage session
- **Critical Issue**: Hardcoded credentials in source code
- **Session Management**: Simple boolean flag in localStorage
- **No Encryption**: All data stored in plain text

**Data Security**:
- All data client-side (no network transmission)
- No input sanitization (SQL injection not possible with current setup)
- No access controls beyond basic authentication

### Security Vulnerabilities

1. **Hardcoded Credentials**: Username 'scharway' and password 'Lookup88?' in source
2. **Plain Text Storage**: All data including authentication state unencrypted
3. **Client-Side Only**: No server-side validation or security controls
4. **Session Management**: Basic localStorage flag without expiration

## Performance Architecture

### Optimization Strategies

**Database Performance**:
- In-memory SQLite for fast queries
- Transaction batching for complex operations
- Automatic indexing on primary keys

**React Performance**:
- Context separation to minimize re-renders
- useCallback for stable function references
- Minimal state lifting

**Loading Strategies**:
- Skeleton loading states for weather widget
- Lazy loading not implemented (small codebase)
- No code splitting currently configured

### Performance Considerations

**Strengths**:
- Client-side storage eliminates network latency
- SQLite provides efficient querying
- React 19 concurrent features

**Limitations**:
- localStorage has storage size limits
- No data pagination implemented
- Weather API calls on every component mount

## Integration Points

### External Dependencies

**SQL.js Integration**:
- WASM binary loaded from jsdelivr CDN
- Initialization required before any database operations
- Error handling for WASM loading failures

**Open-Meteo Weather API**:
- RESTful API with query parameters
- No authentication required
- Rate limiting not implemented

**TailwindCSS**:
- Loaded via CDN in index.html
- No custom configuration
- Utility-first styling approach

### Browser APIs Used

- **localStorage**: Data persistence and authentication
- **crypto.randomUUID()**: Unique ID generation
- **fetch()**: Weather API calls
- **Date**: Date/time manipulation throughout application

## Development Architecture

### Build Configuration (vite.config.ts)

**Current Setup**:
- TypeScript compilation
- Path aliases for cleaner imports
- Environment variable processing (Gemini API - unused)

**Development Features**:
- Hot module replacement
- Fast refresh for React components
- TypeScript error checking

### Project Structure Patterns

**Separation of Concerns**: Clear boundaries between UI, state, and data layers
**Feature Organization**: Components grouped by functionality
**Type Safety**: Comprehensive TypeScript coverage
**Modern Patterns**: Hooks, context, and functional components

## Migration Considerations

### Current Architecture Strengths
- Clean separation of concerns
- Comprehensive type safety
- Transaction-safe data operations
- Responsive design implementation

### Architecture Limitations for Production
- Client-side only (no server state)
- Hardcoded authentication
- No data backup or recovery
- Limited to single device usage

### Migration Path Implications
- Database schema well-defined for backend migration
- Component structure supports API integration
- Context pattern can integrate with server state management
- Type definitions ready for API contract implementation

This architecture provides a solid foundation for migration to a full-stack application while maintaining the existing user experience and functionality.