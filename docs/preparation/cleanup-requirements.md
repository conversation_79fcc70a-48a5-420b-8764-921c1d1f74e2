# LifeTracker Production Cleanup Requirements

## Executive Summary

This document outlines specific cleanup tasks required to prepare the LifeTracker codebase for production deployment. Tasks are prioritized by criticality and organized by category for systematic execution. All changes should preserve existing functionality while removing development artifacts and security vulnerabilities.

## Critical Security Issues (Must Fix Before Deployment)

### 1. Remove Hardcoded Authentication Credentials
**Priority**: CRITICAL  
**Files**: `contexts/AuthContext.tsx`
**Location**: Lines 29-30
**Current Code**:
```typescript
if (username === 'scharway' && password === 'Lookup88?') {
```
**Required Action**: 
- Replace with proper authentication system or remove hardcoded credentials
- Implement environment-based configuration for any default accounts
- Consider implementing user registration system

### 2. Remove Personal Information from Profile Defaults
**Priority**: CRITICAL  
**Files**: `views/ProfileView.tsx`
**Location**: Lines 5-6
**Current Code**:
```typescript
const [fullName, setFullName] = useState('<PERSON>');
const [username, setUsername] = useState('schar<PERSON>');
```
**Required Action**:
- Replace with generic defaults like 'User' or empty strings
- Ensure no personal data is exposed in production

## Gemini API Cleanup (High Priority)

### 3. Remove Gemini API Configuration from Vite
**Priority**: HIGH  
**Files**: `vite.config.ts`
**Location**: Lines 8-9
**Current Code**:
```typescript
define: {
    'process.env.API_KEY': JSON.stringify(env.GEMINI_API_KEY),
    'process.env.GEMINI_API_KEY': JSON.stringify(env.GEMINI_API_KEY)
}
```
**Required Action**: Remove both lines entirely

### 4. Remove Environment File References
**Priority**: HIGH  
**Files**: `.env.local`
**Location**: Line 1
**Current Code**: `GEMINI_API_KEY=PLACEHOLDER_API_KEY`
**Required Action**: Remove entire line or delete file if no other environment variables

### 5. Update README Documentation
**Priority**: HIGH  
**Files**: `README.md`
**Location**: Line 12
**Current Code**: `2. Set the GEMINI_API_KEY in [.env.local](.env.local) to your Gemini API key`
**Required Action**: Remove step entirely and renumber subsequent steps

### 6. Update CLAUDE.md Documentation
**Priority**: HIGH  
**Files**: `CLAUDE.md`
**Location**: Line 175
**Current Code**: Reference to unused Gemini API integration
**Required Action**: Remove or update documentation to reflect cleanup

## Mock Data and Placeholder Content (High Priority)

### 7. Update Initial Seed Tasks with Current Dates
**Priority**: HIGH  
**Files**: `contexts/TaskContext.tsx`
**Location**: Lines 8-29
**Current Issues**:
- `dueDate: "2024-08-15"` (past date)
- `dueDate: "2024-09-05"` (past date)

**Required Action**:
- Replace with relative dates (e.g., next month, next week)
- Use dynamic date calculation to ensure future dates
- Consider making seed tasks more generic/universally applicable

**Suggested Implementation**:
```typescript
const today = new Date();
const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 15);
const nextWeek = new Date(today.setDate(today.getDate() + 7));

// Use nextMonth.toISOString().split('T')[0] for dueDate format
```

### 8. Remove Mock Feedback Messages
**Priority**: HIGH  
**Files**: `views/ProfileView.tsx`, `views/SettingsView.tsx`
**Locations**:
- `ProfileView.tsx` line 17: `'Profile updated successfully! (This is a mock update)'`
- `ProfileView.tsx` line 36: `'Password changed successfully! (This is a mock update)'`
- `SettingsView.tsx` line 13: `"Account deleted (mock). Logging out..."`

**Required Action**:
- Remove "(This is a mock update)" and "(mock)" from messages
- Implement actual functionality or remove features entirely
- Replace with generic success messages

## Location and Configuration Issues (Medium Priority)

### 9. Make Weather Location Configurable
**Priority**: MEDIUM  
**Files**: `services/weatherService.ts`
**Location**: Lines 5-9
**Current Code**:
```typescript
const ST_LOUIS_COORDS = {
    lat: 38.6270,
    lon: -90.1994,
    name: 'St. Louis, MO'
};
```
**Required Action**:
- Move to user settings or application configuration
- Implement location detection or user selection
- Provide fallback to default location if user location unavailable

## User Experience Improvements (Medium Priority)

### 10. Replace Alert/Confirm Dialogs with Proper UI
**Priority**: MEDIUM  
**Files**: Multiple
**Locations**:
- `components/tasks/TaskFormModal.tsx` line 58: `alert('Title is required.');`
- Multiple `window.confirm()` usages for deletion confirmations

**Required Action**:
- Replace alert() with toast notifications or inline error messages
- Replace window.confirm() with custom modal components
- Improve user experience with modern UI patterns

### 11. Enhance Form Validation Feedback
**Priority**: MEDIUM  
**Files**: `components/tasks/TaskFormModal.tsx`
**Current Issue**: Basic alert() for validation errors
**Required Action**:
- Implement inline form validation
- Add visual feedback for form errors
- Improve accessibility with proper error associations

## Development Artifacts (Low Priority)

### 12. Review Console Logging Strategy
**Priority**: LOW  
**Files**: `services/dbService.ts`
**Locations**: Lines 28, 41, 122
**Current Code**: `console.error()` statements for database errors
**Assessment**: These may be appropriate for production but should be reviewed
**Recommended Action**: 
- Keep error logging but ensure log levels are appropriate
- Consider implementing structured logging
- Ensure no sensitive data is logged

### 13. Clean Package.json Scripts
**Priority**: LOW  
**Files**: `package.json`
**Current State**: Missing lint and test scripts
**Recommended Action**:
- Add lint script for code quality
- Add test script when tests are implemented
- Consider adding build validation scripts

## Implementation Strategy

### Phase 1: Security Critical (Immediate)
1. Remove hardcoded credentials (Task #1)
2. Remove personal information (Task #2)
3. Update seed task dates (Task #7)

### Phase 2: API Cleanup (Day 1)
1. Remove Gemini API references (Tasks #3-6)
2. Remove mock feedback messages (Task #8)

### Phase 3: User Experience (Day 2)
1. Make weather location configurable (Task #9)
2. Replace alert/confirm dialogs (Task #10)
3. Enhance form validation (Task #11)

### Phase 4: Polish (Day 3)
1. Review logging strategy (Task #12)
2. Clean package.json (Task #13)
3. Final testing and validation

## Validation Checklist

After completing cleanup tasks:

- [ ] No hardcoded credentials remain in codebase
- [ ] No personal information exposed in production build
- [ ] All Gemini API references removed
- [ ] Seed tasks use current/future dates
- [ ] No mock messages visible to users
- [ ] Weather service handles location appropriately
- [ ] Form validation provides good user experience
- [ ] No development-only code artifacts remain
- [ ] Build process completes without warnings
- [ ] Application functions correctly with clean data

## Testing Requirements

Before marking cleanup complete:

1. **Fresh Installation Test**: Deploy to clean environment and verify initialization
2. **Authentication Test**: Verify login system works with new configuration
3. **Task Management Test**: Verify all CRUD operations function correctly
4. **Weather Widget Test**: Verify weather service works with new location handling
5. **User Profile Test**: Verify profile operations work without mock messages
6. **Database Test**: Verify SQLite operations work correctly after cleanup

## Risk Assessment

### Low Risk Changes
- Removing unused Gemini API references
- Updating documentation
- Removing mock messages
- Updating seed task dates

### Medium Risk Changes  
- Modifying authentication logic
- Changing weather service configuration
- Replacing alert/confirm dialogs

### High Risk Changes
- Modifying database service error handling
- Changing core authentication flow

## Notes for Implementation

- Preserve all existing functionality during cleanup
- Test each change incrementally to avoid breaking changes
- Consider feature flags for any major modifications
- Document any architectural decisions made during cleanup
- Keep backup of original codebase before starting cleanup process