# LifeTracker Deployment Architecture

## Executive Summary

This document defines the comprehensive deployment architecture for LifeTracker's production environment. The design focuses on 30-year operational reliability, security, maintainability, and seamless transition from the current client-side implementation to a production-ready backend system.

## Production Environment Overview

### Deployment Goals
- **30-Year Operational Lifespan**: Design for long-term reliability and maintainability
- **Zero-Downtime Transition**: Seamless migration from client-side to server-side
- **Security First**: Comprehensive security measures for production environment
- **Minimal Maintenance**: Automated maintenance and monitoring
- **Cost Efficiency**: Optimize for single-user application requirements
- **Backup Strategy**: Reliable data backup and recovery procedures

### Technology Stack for Production
```
┌─────────────────────────────────────────────────────────────────┐
│                    Production Stack                            │
├─────────────────────────────────────────────────────────────────┤
│  Frontend (Static Files)                                       │
│  ├── React 19 + TypeScript (Production Build)                  │
│  ├── Vite Production Bundle                                    │
│  ├── Static Asset Optimization                                 │
│  └── Service Worker (Future Enhancement)                       │
├─────────────────────────────────────────────────────────────────┤
│  Backend Application                                           │
│  ├── Node.js 20+ LTS                                          │
│  ├── Express.js Framework                                      │
│  ├── Better-SQLite3 Database                                   │
│  ├── PM2 Process Manager                                       │
│  └── Security Middleware Stack                                 │
├─────────────────────────────────────────────────────────────────┤
│  Operating System                                              │
│  ├── Ubuntu 22.04 LTS (Recommended)                           │
│  ├── CentOS Stream 9 (Alternative)                            │
│  ├── Docker Container (Optional)                               │
│  └── Systemd Service Management                                │
├─────────────────────────────────────────────────────────────────┤
│  Infrastructure                                                │
│  ├── Single VPS/Dedicated Server                               │
│  ├── Nginx Reverse Proxy                                       │
│  ├── SSL/TLS Certificates (Let's Encrypt)                      │
│  ├── Firewall Configuration (UFW/iptables)                     │
│  └── Backup Storage (Local + Cloud)                            │
└─────────────────────────────────────────────────────────────────┘
```

## Server Architecture

### Hardware Requirements

#### Minimum Requirements (Single User)
```
CPU: 1 vCPU (2.0 GHz)
RAM: 1 GB
Storage: 20 GB SSD
Network: 1 Mbps bandwidth
OS: Ubuntu 22.04 LTS
```

#### Recommended Specifications (30-Year Reliability)
```
CPU: 2 vCPU (2.4 GHz)
RAM: 2 GB
Storage: 50 GB SSD with backup space
Network: 10 Mbps bandwidth
OS: Ubuntu 22.04 LTS
Backup: Additional 100 GB cloud storage
```

#### Storage Planning (30-Year Projection)
```
Database Growth Estimation:
- Average task size: 1 KB
- Tasks per day: 10
- Daily growth: 10 KB
- Yearly growth: 3.65 MB
- 30-year growth: ~110 MB

Total Storage Allocation:
- Application files: 500 MB
- Database (30 years): 200 MB
- Logs and backups: 2 GB
- OS and system: 5 GB
- Free space buffer: 12 GB
- Total: 20 GB minimum
```

### Directory Structure

#### Production File Layout
```
/opt/lifetracker/
├── app/                          # Application root
│   ├── backend/                  # Backend application
│   │   ├── dist/                 # Compiled JavaScript
│   │   ├── src/                  # Source code
│   │   ├── package.json          # Dependencies
│   │   └── ecosystem.config.js   # PM2 configuration
│   ├── frontend/                 # Frontend static files
│   │   ├── dist/                 # Production build
│   │   └── assets/               # Static assets
│   └── shared/                   # Shared configuration
│       ├── nginx/                # Nginx configuration
│       └── ssl/                  # SSL certificates
├── data/                         # Application data
│   ├── database/                 # SQLite database files
│   │   ├── lifetracker.db       # Main database
│   │   └── backups/             # Database backups
│   └── logs/                     # Application logs
│       ├── access.log           # Nginx access logs
│       ├── error.log            # Application error logs
│       └── app.log              # Application logs
├── scripts/                      # Maintenance scripts
│   ├── backup.sh                # Database backup script
│   ├── maintenance.sh           # Database maintenance
│   ├── deploy.sh                # Deployment script
│   └── health-check.sh          # Health monitoring
└── config/                       # Configuration files
    ├── environment/              # Environment configurations
    ├── ssl/                      # SSL certificates
    └── nginx/                    # Nginx configurations
```

## Process Management

### PM2 Configuration

#### PM2 Ecosystem Configuration
```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'lifetracker-api',
    script: './dist/app.js',
    cwd: '/opt/lifetracker/app/backend',
    instances: 1, // Single instance for SQLite
    exec_mode: 'fork', // Fork mode for SQLite compatibility
    
    // Environment configuration
    env: {
      NODE_ENV: 'production',
      PORT: 3001,
      JWT_SECRET: process.env.JWT_SECRET,
      DATABASE_PATH: '/opt/lifetracker/data/database/lifetracker.db',
      LOG_LEVEL: 'warn'
    },
    
    // Process management
    max_memory_restart: '500M',
    restart_delay: 5000,
    max_restarts: 10,
    min_uptime: '10s',
    
    // Logging
    log_file: '/opt/lifetracker/data/logs/app.log',
    error_file: '/opt/lifetracker/data/logs/error.log',
    out_file: '/opt/lifetracker/data/logs/out.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Monitoring
    monitoring: false, // Disable PM2 monitoring for privacy
    
    // Auto-restart configuration
    watch: false, // No file watching in production
    ignore_watch: ['node_modules', 'logs', 'data'],
    
    // Advanced options
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 10000
  }]
};
```

#### Service Management Scripts
```bash
#!/bin/bash
# /opt/lifetracker/scripts/service-control.sh

start_service() {
    echo "Starting LifeTracker service..."
    cd /opt/lifetracker/app/backend
    pm2 start ecosystem.config.js
    pm2 save
}

stop_service() {
    echo "Stopping LifeTracker service..."
    pm2 stop lifetracker-api
}

restart_service() {
    echo "Restarting LifeTracker service..."
    pm2 restart lifetracker-api
}

status_service() {
    pm2 status lifetracker-api
    pm2 logs lifetracker-api --lines 50
}

case "$1" in
    start)   start_service ;;
    stop)    stop_service ;;
    restart) restart_service ;;
    status)  status_service ;;
    *)       echo "Usage: $0 {start|stop|restart|status}" ;;
esac
```

### Systemd Service Configuration

#### System Service Definition
```ini
# /etc/systemd/system/lifetracker.service
[Unit]
Description=LifeTracker Personal Task Management Application
After=network.target

[Service]
Type=forking
User=lifetracker
Group=lifetracker
WorkingDirectory=/opt/lifetracker/app/backend
ExecStart=/usr/bin/pm2 start ecosystem.config.js --name lifetracker-api
ExecReload=/usr/bin/pm2 restart lifetracker-api
ExecStop=/usr/bin/pm2 stop lifetracker-api
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=lifetracker

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/lifetracker/data
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true

[Install]
WantedBy=multi-user.target
```

## Web Server Configuration

### Nginx Configuration

#### Main Nginx Configuration
```nginx
# /etc/nginx/sites-available/lifetracker
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    
    # SSL Configuration
    ssl_certificate /opt/lifetracker/config/ssl/fullchain.pem;
    ssl_certificate_key /opt/lifetracker/config/ssl/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self'; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data: https:; connect-src 'self' https://api.open-meteo.com; font-src 'self' https://fonts.gstatic.com; object-src 'none'; media-src 'self'; frame-src 'none';" always;
    
    # Root directory for static files
    root /opt/lifetracker/app/frontend/dist;
    index index.html;
    
    # Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Static file serving with caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }
    
    # API proxy to backend
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Rate limiting
        limit_req zone=api burst=10 nodelay;
    }
    
    # SPA routing - serve index.html for all routes
    location / {
        try_files $uri $uri/ /index.html;
        
        # No caching for HTML files
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
        add_header Expires "0";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ \.(env|config|log)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}

# Rate limiting configuration
http {
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;
    limit_req_zone $binary_remote_addr zone=global:10m rate=100r/m;
}
```

## Security Configuration

### Firewall Setup

#### UFW Firewall Rules
```bash
#!/bin/bash
# /opt/lifetracker/scripts/setup-firewall.sh

# Reset UFW to defaults
ufw --force reset

# Default policies
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (change port if using non-standard)
ufw allow 22/tcp

# Allow HTTP and HTTPS
ufw allow 80/tcp
ufw allow 443/tcp

# Allow specific outgoing connections
ufw allow out 53/udp    # DNS
ufw allow out 80/tcp    # HTTP (for updates)
ufw allow out 443/tcp   # HTTPS (for updates, weather API)
ufw allow out 123/udp   # NTP

# Rate limiting for SSH
ufw limit ssh

# Enable firewall
ufw --force enable

# Show status
ufw status verbose
```

### SSL/TLS Configuration

#### Let's Encrypt Setup Script
```bash
#!/bin/bash
# /opt/lifetracker/scripts/setup-ssl.sh

DOMAIN="yourdomain.com"
EMAIL="<EMAIL>"

# Install certbot
apt update
apt install -y certbot python3-certbot-nginx

# Obtain certificate
certbot --nginx -d $DOMAIN -d www.$DOMAIN \
    --email $EMAIL \
    --agree-tos \
    --non-interactive \
    --redirect

# Test automatic renewal
certbot renew --dry-run

# Setup automatic renewal cron job
cat > /etc/cron.d/certbot << 'EOF'
0 12 * * * root test -x /usr/bin/certbot -a \! -d /run/systemd/system && perl -e 'sleep int(rand(43200))' && certbot -q renew
EOF

echo "SSL certificate installed and auto-renewal configured"
```

### System Hardening

#### Security Hardening Script
```bash
#!/bin/bash
# /opt/lifetracker/scripts/harden-system.sh

# Update system
apt update && apt upgrade -y

# Install security updates automatically
apt install -y unattended-upgrades
dpkg-reconfigure -plow unattended-upgrades

# Configure automatic security updates
cat > /etc/apt/apt.conf.d/50unattended-upgrades << 'EOF'
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}-security";
    "${distro_id}ESMApps:${distro_codename}-apps-security";
    "${distro_id}ESM:${distro_codename}-infra-security";
};
Unattended-Upgrade::AutoFixInterruptedDpkg "true";
Unattended-Upgrade::MinimalSteps "true";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
EOF

# Disable root login and password authentication
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
systemctl restart ssh

# Install fail2ban
apt install -y fail2ban
cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[ssh]
enabled = true
port = ssh
filter = sshd
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
port = http,https
filter = nginx-http-auth
logpath = /var/log/nginx/error.log
maxretry = 3
EOF

systemctl enable fail2ban
systemctl start fail2ban

echo "System hardening completed"
```

## Backup and Recovery

### Backup Strategy

#### Automated Backup Script
```bash
#!/bin/bash
# /opt/lifetracker/scripts/backup.sh

set -e

BACKUP_DIR="/opt/lifetracker/data/database/backups"
DATABASE_PATH="/opt/lifetracker/data/database/lifetracker.db"
RETENTION_DAYS=30
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# Function to create backup
create_backup() {
    local backup_file="$BACKUP_DIR/lifetracker_backup_$TIMESTAMP.db"
    
    # Use SQLite VACUUM INTO for consistent backup
    sqlite3 "$DATABASE_PATH" "VACUUM INTO '$backup_file';"
    
    # Verify backup integrity
    sqlite3 "$backup_file" "PRAGMA integrity_check;" | grep -q "ok"
    
    if [ $? -eq 0 ]; then
        echo "Backup created successfully: $backup_file"
        
        # Compress backup
        gzip "$backup_file"
        echo "Backup compressed: $backup_file.gz"
        
        # Upload to cloud storage (optional)
        upload_to_cloud "$backup_file.gz"
        
    else
        echo "Backup integrity check failed, removing corrupted backup"
        rm -f "$backup_file"
        exit 1
    fi
}

# Function to upload to cloud storage (customize as needed)
upload_to_cloud() {
    local backup_file="$1"
    
    # Example: AWS S3 upload (requires AWS CLI configured)
    # aws s3 cp "$backup_file" s3://your-backup-bucket/lifetracker/
    
    # Example: rsync to remote server
    # rsync -az "$backup_file" user@backup-server:/path/to/backups/
    
    echo "Cloud upload placeholder - configure as needed"
}

# Function to clean old backups
cleanup_old_backups() {
    find "$BACKUP_DIR" -name "lifetracker_backup_*.db.gz" -mtime +$RETENTION_DAYS -delete
    echo "Cleaned up backups older than $RETENTION_DAYS days"
}

# Main execution
echo "Starting backup process at $(date)"
create_backup
cleanup_old_backups
echo "Backup process completed at $(date)"
```

#### Backup Cron Configuration
```bash
# Add to crontab -e for root or lifetracker user
# Daily backup at 1:00 AM
0 1 * * * /opt/lifetracker/scripts/backup.sh >> /opt/lifetracker/data/logs/backup.log 2>&1

# Weekly backup verification
0 2 * * 0 /opt/lifetracker/scripts/verify-backups.sh >> /opt/lifetracker/data/logs/backup-verify.log 2>&1
```

### Recovery Procedures

#### Database Recovery Script
```bash
#!/bin/bash
# /opt/lifetracker/scripts/recover.sh

set -e

BACKUP_DIR="/opt/lifetracker/data/database/backups"
DATABASE_PATH="/opt/lifetracker/data/database/lifetracker.db"
SERVICE_NAME="lifetracker"

# Function to list available backups
list_backups() {
    echo "Available backups:"
    ls -la "$BACKUP_DIR"/lifetracker_backup_*.db.gz | nl
}

# Function to restore from backup
restore_backup() {
    local backup_file="$1"
    
    if [ ! -f "$backup_file" ]; then
        echo "Backup file not found: $backup_file"
        exit 1
    fi
    
    echo "Stopping LifeTracker service..."
    systemctl stop "$SERVICE_NAME"
    
    # Create backup of current database
    if [ -f "$DATABASE_PATH" ]; then
        mv "$DATABASE_PATH" "$DATABASE_PATH.before_restore_$(date +%Y%m%d_%H%M%S)"
    fi
    
    # Restore from backup
    echo "Restoring database from backup..."
    gunzip -c "$backup_file" > "$DATABASE_PATH"
    
    # Verify restored database
    sqlite3 "$DATABASE_PATH" "PRAGMA integrity_check;" | grep -q "ok"
    
    if [ $? -eq 0 ]; then
        echo "Database restored successfully"
        
        # Set correct permissions
        chown lifetracker:lifetracker "$DATABASE_PATH"
        chmod 600 "$DATABASE_PATH"
        
        # Start service
        systemctl start "$SERVICE_NAME"
        echo "LifeTracker service restarted"
        
    else
        echo "Restored database failed integrity check"
        exit 1
    fi
}

# Main execution
if [ $# -eq 0 ]; then
    list_backups
    echo "Usage: $0 <backup_file_path>"
    exit 1
fi

restore_backup "$1"
```

## Monitoring and Maintenance

### Health Monitoring

#### System Health Check Script
```bash
#!/bin/bash
# /opt/lifetracker/scripts/health-check.sh

set -e

DATABASE_PATH="/opt/lifetracker/data/database/lifetracker.db"
SERVICE_NAME="lifetracker"
LOG_FILE="/opt/lifetracker/data/logs/health-check.log"

# Function to log with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Check service status
check_service() {
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_message "Service is running"
        return 0
    else
        log_message "ERROR: Service is not running"
        return 1
    fi
}

# Check database health
check_database() {
    if [ -f "$DATABASE_PATH" ]; then
        # Check database integrity
        result=$(sqlite3 "$DATABASE_PATH" "PRAGMA integrity_check;" 2>/dev/null)
        if [ "$result" = "ok" ]; then
            log_message "Database integrity check passed"
            return 0
        else
            log_message "ERROR: Database integrity check failed: $result"
            return 1
        fi
    else
        log_message "ERROR: Database file not found"
        return 1
    fi
}

# Check disk space
check_disk_space() {
    usage=$(df /opt/lifetracker | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$usage" -lt 80 ]; then
        log_message "Disk space usage: ${usage}% (OK)"
        return 0
    else
        log_message "WARNING: High disk usage: ${usage}%"
        return 1
    fi
}

# Check API endpoint
check_api() {
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:3001/api/health 2>/dev/null)
    if [ "$response" = "200" ]; then
        log_message "API health check passed"
        return 0
    else
        log_message "ERROR: API health check failed with code: $response"
        return 1
    fi
}

# Main health check
main() {
    log_message "Starting health check"
    
    local errors=0
    
    check_service || ((errors++))
    check_database || ((errors++))
    check_disk_space || ((errors++))
    check_api || ((errors++))
    
    if [ $errors -eq 0 ]; then
        log_message "All health checks passed"
        exit 0
    else
        log_message "Health check completed with $errors errors"
        exit 1
    fi
}

main
```

### Maintenance Tasks

#### Automated Maintenance Script
```bash
#!/bin/bash
# /opt/lifetracker/scripts/maintenance.sh

set -e

DATABASE_PATH="/opt/lifetracker/data/database/lifetracker.db"
LOG_FILE="/opt/lifetracker/data/logs/maintenance.log"

log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Database maintenance
database_maintenance() {
    log_message "Starting database maintenance"
    
    # VACUUM to reclaim space and defragment
    sqlite3 "$DATABASE_PATH" "VACUUM;"
    log_message "Database VACUUM completed"
    
    # ANALYZE to update query planner statistics
    sqlite3 "$DATABASE_PATH" "ANALYZE;"
    log_message "Database ANALYZE completed"
    
    # Update maintenance timestamp
    sqlite3 "$DATABASE_PATH" "UPDATE database_metadata SET last_vacuum = datetime('now'), last_analyze = datetime('now') WHERE id = 1;"
    log_message "Maintenance timestamps updated"
}

# Log rotation
rotate_logs() {
    log_message "Starting log rotation"
    
    # Rotate application logs
    find /opt/lifetracker/data/logs -name "*.log" -size +10M -exec logrotate -f {} \;
    
    # Keep only last 30 days of logs
    find /opt/lifetracker/data/logs -name "*.log.*" -mtime +30 -delete
    
    log_message "Log rotation completed"
}

# System updates check
check_updates() {
    log_message "Checking for system updates"
    
    # Update package list
    apt update > /dev/null 2>&1
    
    # Check for security updates
    security_updates=$(apt list --upgradable 2>/dev/null | grep -c security || true)
    
    if [ "$security_updates" -gt 0 ]; then
        log_message "WARNING: $security_updates security updates available"
    else
        log_message "No security updates pending"
    fi
}

# Main maintenance routine
main() {
    log_message "Starting maintenance routine"
    
    database_maintenance
    rotate_logs
    check_updates
    
    log_message "Maintenance routine completed"
}

main
```

#### Maintenance Cron Schedule
```bash
# Add to crontab -e
# Database maintenance - weekly on Sunday at 2 AM
0 2 * * 0 /opt/lifetracker/scripts/maintenance.sh

# Health checks - every 15 minutes
*/15 * * * * /opt/lifetracker/scripts/health-check.sh

# System updates check - daily at 6 AM
0 6 * * * /opt/lifetracker/scripts/check-updates.sh

# Log cleanup - daily at midnight
0 0 * * * find /opt/lifetracker/data/logs -name "*.log" -mtime +7 -exec rm {} \;
```

## Deployment Process

### Initial Deployment

#### Deployment Script
```bash
#!/bin/bash
# /opt/lifetracker/scripts/deploy.sh

set -e

REPO_URL="https://github.com/your-username/lifetracker.git"
DEPLOY_USER="lifetracker"
APP_DIR="/opt/lifetracker"

# Create deployment user
create_user() {
    if ! id "$DEPLOY_USER" &>/dev/null; then
        useradd -r -s /bin/bash -d "$APP_DIR" "$DEPLOY_USER"
        echo "Created user: $DEPLOY_USER"
    fi
}

# Setup directory structure
setup_directories() {
    mkdir -p "$APP_DIR"/{app,data,scripts,config}
    mkdir -p "$APP_DIR/data"/{database/backups,logs}
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$APP_DIR"
    chmod 755 "$APP_DIR"
    chmod 700 "$APP_DIR/data"
    echo "Directory structure created"
}

# Install dependencies
install_dependencies() {
    # Update system
    apt update && apt upgrade -y
    
    # Install Node.js
    curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
    apt install -y nodejs
    
    # Install PM2 globally
    npm install -g pm2
    
    # Install other dependencies
    apt install -y nginx sqlite3 ufw fail2ban
    
    echo "Dependencies installed"
}

# Deploy application
deploy_application() {
    cd "$APP_DIR"
    
    # Clone or update repository
    if [ -d "app/.git" ]; then
        cd app && git pull origin main
    else
        git clone "$REPO_URL" app
    fi
    
    # Build backend
    cd app/backend
    npm install --production
    npm run build
    
    # Build frontend
    cd ../frontend
    npm install
    npm run build
    
    # Set permissions
    chown -R "$DEPLOY_USER:$DEPLOY_USER" "$APP_DIR/app"
    
    echo "Application deployed"
}

# Configure services
configure_services() {
    # Copy service files
    cp "$APP_DIR/app/deployment/lifetracker.service" /etc/systemd/system/
    cp "$APP_DIR/app/deployment/nginx.conf" /etc/nginx/sites-available/lifetracker
    
    # Enable services
    systemctl daemon-reload
    systemctl enable lifetracker
    systemctl enable nginx
    
    # Configure nginx
    ln -sf /etc/nginx/sites-available/lifetracker /etc/nginx/sites-enabled/
    rm -f /etc/nginx/sites-enabled/default
    
    echo "Services configured"
}

# Main deployment
main() {
    echo "Starting LifeTracker deployment..."
    
    create_user
    setup_directories
    install_dependencies
    deploy_application
    configure_services
    
    echo "Deployment completed successfully!"
    echo "Run 'systemctl start lifetracker' to start the application"
}

main
```

### Update Process

#### Application Update Script
```bash
#!/bin/bash
# /opt/lifetracker/scripts/update.sh

set -e

APP_DIR="/opt/lifetracker"
SERVICE_NAME="lifetracker"

# Backup before update
backup_before_update() {
    echo "Creating backup before update..."
    /opt/lifetracker/scripts/backup.sh
    echo "Backup completed"
}

# Update application
update_application() {
    echo "Updating application..."
    
    cd "$APP_DIR/app"
    git pull origin main
    
    # Update backend
    cd backend
    npm install --production
    npm run build
    
    # Update frontend
    cd ../frontend
    npm install
    npm run build
    
    echo "Application updated"
}

# Restart services
restart_services() {
    echo "Restarting services..."
    
    systemctl restart "$SERVICE_NAME"
    systemctl reload nginx
    
    # Wait for service to start
    sleep 10
    
    # Verify service is running
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        echo "Service restarted successfully"
    else
        echo "ERROR: Service failed to start"
        exit 1
    fi
}

# Main update process
main() {
    echo "Starting LifeTracker update..."
    
    backup_before_update
    update_application
    restart_services
    
    echo "Update completed successfully!"
}

main
```

This deployment architecture provides a comprehensive foundation for 30-year operational reliability while maintaining security, performance, and ease of maintenance for the LifeTracker application.