# LifeTracker API Specification

## Executive Summary

This document defines the complete REST API specification for the LifeTracker backend service. The API design mirrors the existing frontend dbService.ts operations to ensure seamless integration without requiring frontend changes. All endpoints follow RESTful principles with comprehensive error handling, input validation, and security measures.

## API Overview

### Base Configuration
- **Base URL**: `http://localhost:3001/api` (development)
- **Protocol**: HTTP/HTTPS
- **Content-Type**: `application/json`
- **Authentication**: JWT Bearer tokens
- **Rate Limiting**: 100 requests per minute per IP for auth endpoints, 1000 for others

### Global Headers
```http
Content-Type: application/json
Authorization: Bearer <jwt_token> (for protected routes)
X-Request-ID: <uuid> (optional, for tracing)
```

### Standard Response Format
```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    field?: string;
    details?: any;
  };
  metadata: {
    timestamp: string;
    requestId: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      hasNext: boolean;
    };
  };
}
```

## Authentication Endpoints

### POST /api/auth/login
Authenticates user credentials and returns JWT token.

**Request Body:**
```typescript
{
  username: string; // Required, min 3 chars
  password: string; // Required, min 8 chars
}
```

**Response (200):**
```typescript
{
  success: true,
  data: {
    token: string; // JWT token valid for 5 minutes
    user: {
      id: number;
      username: string;
    };
    expiresAt: string; // ISO timestamp
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `400` - Invalid request body
- `401` - Invalid credentials
- `429` - Too many login attempts

**Rate Limiting:** 5 attempts per minute per IP

### POST /api/auth/logout
Invalidates the current JWT token.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```typescript
{
  success: true,
  data: {
    message: "Successfully logged out"
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `401` - Invalid or expired token

### GET /api/auth/session
Validates current session and returns user information.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```typescript
{
  success: true,
  data: {
    user: {
      id: number;
      username: string;
    };
    expiresAt: string; // ISO timestamp
    isValid: boolean;
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `401` - Invalid or expired token

### POST /api/auth/refresh
Refreshes JWT token if within refresh window.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```typescript
{
  success: true,
  data: {
    token: string; // New JWT token
    expiresAt: string; // ISO timestamp
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `401` - Token expired or invalid
- `403` - Token not within refresh window

## Task Management Endpoints

### GET /api/tasks
Retrieves all tasks for the authenticated user.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
```typescript
{
  completed?: boolean; // Filter by completion status
  category?: string; // Filter by category
  priority?: 'Low' | 'Medium' | 'High'; // Filter by priority
  dueDate?: string; // Filter by due date (YYYY-MM-DD)
  search?: string; // Search in title and description
  limit?: number; // Results per page (default 100)
  offset?: number; // Skip results (default 0)
}
```

**Response (200):**
```typescript
{
  success: true,
  data: Task[], // Array of Task objects
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here",
    pagination: {
      page: 1,
      limit: 100,
      total: 25,
      hasNext: false
    }
  }
}

interface Task {
  id: string; // UUID
  title: string;
  description?: string;
  dueDate: string; // YYYY-MM-DD
  dueTime: string; // HH:mm
  isCompleted: boolean;
  completedAt?: string; // ISO timestamp
  priority: 'Low' | 'Medium' | 'High';
  category?: string;
  isRecurring: boolean;
  recurrenceType?: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly';
  recurrenceInterval?: number;
  createdAt: string; // ISO timestamp
  updatedAt: string; // ISO timestamp
}
```

**Error Responses:**
- `401` - Invalid or expired token
- `400` - Invalid query parameters

### POST /api/tasks
Creates a new task.

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```typescript
{
  title: string; // Required, max 200 chars
  description?: string; // Optional, max 1000 chars
  dueDate: string; // Required, YYYY-MM-DD format
  dueTime: string; // Required, HH:mm format
  priority: 'Low' | 'Medium' | 'High'; // Required
  category?: string; // Optional, max 50 chars
  isRecurring?: boolean; // Optional, default false
  recurrenceType?: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly'; // Required if isRecurring
  recurrenceInterval?: number; // Optional, default 1, min 1, max 365
}
```

**Response (201):**
```typescript
{
  success: true,
  data: Task, // Created task object with generated id and timestamps
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `400` - Validation errors
- `401` - Invalid or expired token

**Validation Rules:**
- `title`: Required, 1-200 characters
- `dueDate`: Required, valid date format (YYYY-MM-DD)
- `dueTime`: Required, valid time format (HH:mm)
- `priority`: Required, must be 'Low', 'Medium', or 'High'
- `recurrenceType`: Required if `isRecurring` is true
- `recurrenceInterval`: Must be 1-365 if provided

### GET /api/tasks/:id
Retrieves a specific task by ID.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `id` (string): Task UUID

**Response (200):**
```typescript
{
  success: true,
  data: Task, // Task object
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `401` - Invalid or expired token
- `404` - Task not found

### PUT /api/tasks/:id
Updates an existing task.

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Path Parameters:**
- `id` (string): Task UUID

**Request Body:**
```typescript
{
  title?: string; // Optional, max 200 chars
  description?: string; // Optional, max 1000 chars
  dueDate?: string; // Optional, YYYY-MM-DD format
  dueTime?: string; // Optional, HH:mm format
  isCompleted?: boolean; // Optional
  priority?: 'Low' | 'Medium' | 'High'; // Optional
  category?: string; // Optional, max 50 chars
  isRecurring?: boolean; // Optional
  recurrenceType?: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly'; // Optional
  recurrenceInterval?: number; // Optional, min 1, max 365
}
```

**Response (200):**
```typescript
{
  success: true,
  data: Task, // Updated task object
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `400` - Validation errors
- `401` - Invalid or expired token
- `404` - Task not found

**Special Behavior for Recurring Tasks:**
When `isCompleted` is set to `true` for a recurring task, the system:
1. Creates a new completed task instance (non-recurring)
2. Updates the original task's due date to the next occurrence
3. Returns the updated original task (not the completed instance)

### DELETE /api/tasks/:id
Permanently deletes a task.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Path Parameters:**
- `id` (string): Task UUID

**Response (200):**
```typescript
{
  success: true,
  data: {
    message: "Task deleted successfully",
    deletedTaskId: string
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `401` - Invalid or expired token
- `404` - Task not found

### POST /api/tasks/seed
Seeds initial tasks for new users (internal use).

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```typescript
{
  tasks: Array<{
    title: string;
    description?: string;
    dueDate: string;
    dueTime: string;
    priority: 'Low' | 'Medium' | 'High';
    category?: string;
    isRecurring?: boolean;
    recurrenceType?: 'Daily' | 'Weekly' | 'Monthly' | 'Yearly';
    recurrenceInterval?: number;
  }>
}
```

**Response (201):**
```typescript
{
  success: true,
  data: {
    seededTasks: Task[], // Array of created tasks
    count: number
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `400` - Validation errors or already seeded
- `401` - Invalid or expired token

## Settings Management Endpoints

### GET /api/settings
Retrieves all user settings.

**Headers:**
```http
Authorization: Bearer <jwt_token>
```

**Response (200):**
```typescript
{
  success: true,
  data: {
    showCompleted: boolean;
    seeded: boolean;
    // Additional settings as they are added
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `401` - Invalid or expired token

### PUT /api/settings
Updates user settings (bulk update).

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```typescript
{
  showCompleted?: boolean;
  seeded?: boolean;
  // Additional settings as they are added
}
```

**Response (200):**
```typescript
{
  success: true,
  data: {
    showCompleted: boolean;
    seeded: boolean;
    // Updated settings object
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `400` - Invalid setting values
- `401` - Invalid or expired token

### PUT /api/settings/:key
Updates a specific setting.

**Headers:**
```http
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Path Parameters:**
- `key` (string): Setting key name

**Request Body:**
```typescript
{
  value: boolean | string | number; // Setting value
}
```

**Response (200):**
```typescript
{
  success: true,
  data: {
    key: string;
    value: any;
    updatedAt: string;
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `400` - Invalid setting key or value
- `401` - Invalid or expired token

**Supported Setting Keys:**
- `showCompleted` (boolean): Whether to display completed tasks
- `seeded` (boolean): Whether initial tasks have been seeded

## System Health Endpoints

### GET /api/health
Returns system health status.

**Response (200):**
```typescript
{
  success: true,
  data: {
    status: 'healthy' | 'unhealthy',
    timestamp: string,
    uptime: number, // Seconds
    database: {
      connected: boolean;
      lastCheck: string;
      responseTime: number; // Milliseconds
    },
    memory: {
      used: number; // Bytes
      free: number; // Bytes
      percentage: number;
    }
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `503` - Service unavailable

### GET /api/health/database
Returns detailed database health information.

**Response (200):**
```typescript
{
  success: true,
  data: {
    connected: boolean;
    version: string;
    size: number; // Database file size in bytes
    taskCount: number;
    settingsCount: number;
    lastMaintenance: string;
    integrity: boolean;
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

**Error Responses:**
- `503` - Database connection issues

## Error Handling

### Error Response Format
All error responses follow this consistent format:

```typescript
{
  success: false,
  error: {
    code: string; // Machine-readable error code
    message: string; // Human-readable error message
    field?: string; // Field name for validation errors
    details?: any; // Additional error context
  },
  metadata: {
    timestamp: string;
    requestId: string;
  }
}
```

### Error Codes

#### Authentication Errors (4xx)
- `AUTH_INVALID_CREDENTIALS` - Username or password incorrect
- `AUTH_TOKEN_EXPIRED` - JWT token has expired
- `AUTH_TOKEN_INVALID` - JWT token is malformed or invalid
- `AUTH_RATE_LIMITED` - Too many authentication attempts
- `AUTH_REQUIRED` - Authentication required for this endpoint

#### Validation Errors (400)
- `VALIDATION_REQUIRED` - Required field missing
- `VALIDATION_FORMAT` - Field format invalid
- `VALIDATION_LENGTH` - Field length validation failed
- `VALIDATION_ENUM` - Value not in allowed enum values
- `VALIDATION_DATE` - Invalid date format or value

#### Resource Errors (4xx)
- `RESOURCE_NOT_FOUND` - Requested resource does not exist
- `RESOURCE_CONFLICT` - Resource already exists or conflict

#### Server Errors (5xx)
- `DATABASE_ERROR` - Database operation failed
- `INTERNAL_ERROR` - Unexpected server error
- `SERVICE_UNAVAILABLE` - Service temporarily unavailable

### Rate Limiting

#### Authentication Endpoints
- **Limit**: 5 requests per minute per IP
- **Headers**: 
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset timestamp

#### General API Endpoints
- **Limit**: 1000 requests per minute per IP
- **Headers**: Same as above

#### Rate Limit Exceeded Response (429)
```typescript
{
  success: false,
  error: {
    code: "RATE_LIMIT_EXCEEDED",
    message: "Too many requests. Please try again later.",
    details: {
      limit: 5,
      remaining: 0,
      resetTime: "2025-08-03T10:31:00.000Z"
    }
  },
  metadata: {
    timestamp: "2025-08-03T10:30:00.000Z",
    requestId: "uuid-here"
  }
}
```

## Security Considerations

### Input Validation
- All input validated using Joi schemas
- SQL injection prevention via prepared statements
- XSS prevention via input sanitization
- File upload restrictions (not applicable for current API)

### Authentication Security
- JWT tokens expire after 5 minutes
- Bcrypt with 12 salt rounds for password hashing
- Rate limiting on authentication endpoints
- Secure HTTP headers via Helmet.js

### CORS Configuration
```typescript
{
  origin: ['http://localhost:3000', 'http://localhost:5173'], // Development
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
}
```

### Content Security Policy
- Strict CSP headers for XSS prevention
- HSTS headers for HTTPS enforcement (production)
- X-Frame-Options to prevent clickjacking

## Frontend Integration

### Context Updates Required
The frontend contexts need minimal changes to use the API:

```typescript
// AuthContext - replace direct credential check with API call
const login = async (username: string, password: string) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  });
  
  if (response.ok) {
    const { data } = await response.json();
    localStorage.setItem('authToken', data.token);
    setIsAuthenticated(true);
  } else {
    const { error } = await response.json();
    throw new Error(error.message);
  }
};

// TaskContext - replace dbService calls with API calls
const getTasks = async (): Promise<Task[]> => {
  const response = await fetch('/api/tasks', {
    headers: {
      'Authorization': `Bearer ${getAuthToken()}`
    }
  });
  
  if (response.ok) {
    const { data } = await response.json();
    return data;
  } else {
    throw new Error('Failed to fetch tasks');
  }
};
```

### Error Handling Integration
```typescript
// Utility function for API calls
const apiCall = async (url: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
  });
  
  const result = await response.json();
  
  if (!response.ok) {
    if (response.status === 401) {
      // Token expired, logout user
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    throw new Error(result.error.message);
  }
  
  return result;
};
```

This API specification ensures complete functional parity with the existing frontend implementation while providing a secure, scalable backend foundation.