# LifeTracker Backend System Design

## Executive Summary

This document defines the comprehensive backend architecture for LifeTracker's production deployment. The design transitions from client-side SQLite to a secure Express.js backend while preserving all existing frontend functionality and user experience. The architecture emphasizes 30-year operational reliability, security, and maintainability.

## System Context

### Current State Analysis
- **Frontend**: React 19 + TypeScript application with context-based state management
- **Current DB**: Client-side SQLite via sql.js with localStorage persistence
- **Authentication**: Hardcoded credentials with localStorage session management
- **Data Flow**: Direct frontend-to-database operations through dbService.ts

### Target State Architecture
- **Backend**: Express.js server with SQLite database persistence
- **Database**: Server-side SQLite using better-sqlite3 for production reliability
- **Authentication**: Secure JWT-based authentication with bcrypt password hashing
- **API**: RESTful endpoints mirroring current dbService.ts operations
- **Security**: Comprehensive input validation, rate limiting, and session management

## High-Level Architecture

### System Components

```
┌─────────────────────────────────────────────────────────────────┐
│                     LifeTracker System                         │
├─────────────────────────────────────────────────────────────────┤
│  Frontend (React 19 + TypeScript)                              │
│  ├── AuthContext ──────────────────────┐                       │
│  ├── TaskContext ──────────────────────┼──► API Calls          │
│  ├── SettingsContext ──────────────────┘                       │
│  └── Components (unchanged)                                    │
├─────────────────────────────────────────────────────────────────┤
│  Backend (Express.js + Node.js)                                │
│  ├── Authentication Middleware                                 │
│  ├── Rate Limiting & Security                                  │
│  ├── API Routes                                                │
│  │   ├── /api/auth (login, logout, session)                   │
│  │   ├── /api/tasks (CRUD operations)                         │
│  │   ├── /api/settings (user preferences)                     │
│  │   └── /api/health (system monitoring)                      │
│  ├── Business Logic Layer                                      │
│  │   ├── Task Service (recurring logic)                       │
│  │   ├── Auth Service (JWT management)                        │
│  │   └── Database Service (SQLite operations)                 │
│  └── Database Layer                                            │
│      └── SQLite + better-sqlite3                              │
├─────────────────────────────────────────────────────────────────┤
│  Infrastructure                                                │
│  ├── File System (database storage)                            │
│  ├── Session Store (memory/Redis)                              │
│  └── Logging & Monitoring                                      │
└─────────────────────────────────────────────────────────────────┘
```

## Component Architecture

### Express.js Application Structure

```
/backend
├── src/
│   ├── config/
│   │   ├── database.ts         # SQLite configuration
│   │   ├── auth.ts            # JWT & bcrypt settings
│   │   └── security.ts        # Rate limiting, CORS
│   ├── middleware/
│   │   ├── auth.ts            # JWT verification
│   │   ├── validation.ts      # Input validation
│   │   ├── security.ts        # Security headers
│   │   └── errorHandler.ts    # Global error handling
│   ├── routes/
│   │   ├── auth.ts            # Authentication endpoints
│   │   ├── tasks.ts           # Task management endpoints
│   │   ├── settings.ts        # Settings endpoints
│   │   └── health.ts          # Health check endpoints
│   ├── services/
│   │   ├── authService.ts     # Authentication business logic
│   │   ├── taskService.ts     # Task management logic
│   │   ├── dbService.ts       # Database operations
│   │   └── maintenanceService.ts # DB maintenance tasks
│   ├── models/
│   │   ├── Task.ts            # Task entity definitions
│   │   ├── Settings.ts        # Settings entity definitions
│   │   └── User.ts            # User entity definitions
│   ├── utils/
│   │   ├── validators.ts      # Input validation helpers
│   │   ├── dateUtils.ts       # Date manipulation utilities
│   │   └── security.ts        # Security utility functions
│   ├── types/
│   │   └── index.ts           # TypeScript type definitions
│   └── app.ts                 # Express application setup
├── database/
│   └── lifetracker.db         # SQLite database file
├── logs/                      # Application logs (if needed)
└── package.json
```

### Core Services Design

#### Database Service (dbService.ts)

**Purpose**: Centralized SQLite operations with better-sqlite3 for production reliability

**Key Features**:
- Synchronous operations for better performance
- Prepared statements for SQL injection prevention
- Transaction support for complex operations
- Automatic database maintenance scheduling
- Connection pooling and lifecycle management

**Interface**:
```typescript
interface DatabaseService {
  initialize(): Promise<void>;
  getTasks(): Promise<Task[]>;
  addTask(task: Omit<Task, 'id' | 'createdAt'>): Promise<Task>;
  updateTask(taskId: string, updates: Partial<Task>): Promise<Task>;
  deleteTask(taskId: string): Promise<void>;
  getSettings(): Promise<Settings>;
  updateSetting(key: string, value: any): Promise<void>;
  runMaintenance(): Promise<void>;
  backup(): Promise<string>;
}
```

#### Authentication Service (authService.ts)

**Purpose**: Secure authentication with JWT and bcrypt

**Key Features**:
- Bcrypt password hashing with salt rounds
- JWT token generation and validation
- Session management with automatic expiration
- Rate limiting on authentication attempts
- Secure password storage and verification

**Interface**:
```typescript
interface AuthService {
  login(username: string, password: string): Promise<AuthResult>;
  logout(token: string): Promise<void>;
  validateToken(token: string): Promise<User>;
  refreshToken(token: string): Promise<string>;
  hashPassword(password: string): Promise<string>;
  verifyPassword(password: string, hash: string): Promise<boolean>;
}
```

#### Task Service (taskService.ts)

**Purpose**: Business logic for task management including recurring task handling

**Key Features**:
- Recurring task completion logic
- Task validation and normalization
- Due date calculations
- Category and priority management
- Task search and filtering

**Interface**:
```typescript
interface TaskService {
  createTask(taskData: CreateTaskRequest): Promise<Task>;
  updateTask(taskId: string, updates: UpdateTaskRequest): Promise<Task>;
  deleteTask(taskId: string): Promise<void>;
  getTasks(filters?: TaskFilters): Promise<Task[]>;
  completeRecurringTask(taskId: string): Promise<Task>;
  searchTasks(query: string): Promise<Task[]>;
}
```

### Security Architecture

#### Authentication Flow
1. **Login Request**: POST /api/auth/login with username/password
2. **Credential Validation**: Bcrypt comparison against stored hash
3. **JWT Generation**: Create signed token with user payload and expiration
4. **Response**: Return JWT token and user information
5. **Subsequent Requests**: Include JWT in Authorization header
6. **Token Validation**: Middleware verifies JWT on protected routes
7. **Auto-Logout**: 5-minute inactivity timeout enforced

#### Security Middleware Stack
```typescript
// Middleware execution order
app.use(helmet()); // Security headers
app.use(cors(corsOptions)); // CORS configuration
app.use(rateLimiter); // Rate limiting
app.use(express.json({ limit: '10mb' })); // Body parsing with size limit
app.use(authMiddleware); // JWT validation (protected routes)
app.use(validationMiddleware); // Input validation
app.use(routes); // Application routes
app.use(errorHandler); // Global error handling
```

#### Input Validation Strategy
- **Joi Schema Validation**: All request payloads validated against schemas
- **SQL Injection Prevention**: Prepared statements with parameter binding
- **XSS Protection**: Input sanitization and output encoding
- **CSRF Protection**: CSRF tokens for state-changing operations
- **Rate Limiting**: Per-IP and per-user request limits

### Data Architecture

#### Database Schema Migration
The existing SQLite schema will be preserved with performance enhancements:

```sql
-- Enhanced tasks table with indexes
CREATE TABLE tasks (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    dueDate TEXT NOT NULL,
    dueTime TEXT NOT NULL,
    isCompleted INTEGER NOT NULL DEFAULT 0,
    completedAt TEXT,
    priority TEXT NOT NULL CHECK (priority IN ('Low', 'Medium', 'High')),
    category TEXT,
    isRecurring INTEGER NOT NULL DEFAULT 0,
    recurrenceType TEXT CHECK (recurrenceType IN ('Daily', 'Weekly', 'Monthly', 'Yearly')),
    recurrenceInterval INTEGER,
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Performance indexes
CREATE INDEX idx_tasks_duedate ON tasks(dueDate);
CREATE INDEX idx_tasks_completed ON tasks(isCompleted);
CREATE INDEX idx_tasks_category ON tasks(category);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_recurring ON tasks(isRecurring);

-- Enhanced settings table
CREATE TABLE settings (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updatedAt TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- User credentials table (single user)
CREATE TABLE user_credentials (
    id INTEGER PRIMARY KEY CHECK (id = 1), -- Enforce single user
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

#### Database Maintenance Strategy
- **VACUUM Operations**: Scheduled weekly to reclaim disk space
- **ANALYZE Operations**: Daily statistics updates for query optimization
- **Backup Strategy**: Daily automated backups with rotation
- **Integrity Checks**: Weekly PRAGMA integrity_check
- **Performance Monitoring**: Query execution time tracking

### API Design Principles

#### RESTful Endpoint Design
- **Resource-Based URLs**: `/api/tasks`, `/api/settings`
- **HTTP Methods**: GET, POST, PUT, DELETE for CRUD operations
- **Status Codes**: Appropriate HTTP status codes for all responses
- **Error Handling**: Consistent error response format
- **Pagination**: Cursor-based pagination for large datasets

#### Request/Response Format
```typescript
// Standard API Response Format
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    timestamp: string;
    requestId: string;
    pagination?: PaginationInfo;
  };
}
```

#### Content Negotiation
- **Accept JSON**: All endpoints accept and return JSON
- **Content-Type Validation**: Strict content-type checking
- **Compression**: Gzip compression for responses
- **Caching**: Appropriate cache headers for static content

### Performance Architecture

#### Optimization Strategies
- **Connection Pooling**: Better-sqlite3 connection management
- **Prepared Statements**: Pre-compiled SQL for repeated queries
- **Query Optimization**: Indexed queries for common operations
- **Memory Management**: Efficient data serialization/deserialization
- **Response Caching**: Redis caching for frequent queries (future enhancement)

#### Monitoring and Metrics
- **Response Times**: Track API endpoint performance
- **Database Performance**: Query execution time monitoring
- **Memory Usage**: Node.js heap and database memory tracking
- **Error Rates**: Track 4xx and 5xx response rates
- **Authentication Metrics**: Failed login attempts and session durations

### Error Handling Architecture

#### Error Classification
- **Validation Errors**: 400 Bad Request with field-specific messages
- **Authentication Errors**: 401 Unauthorized for invalid credentials
- **Authorization Errors**: 403 Forbidden for insufficient permissions
- **Not Found Errors**: 404 Not Found for missing resources
- **Server Errors**: 500 Internal Server Error with sanitized messages

#### Error Response Format
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string; // Machine-readable error code
    message: string; // Human-readable message
    field?: string; // Field name for validation errors
    details?: any; // Additional error context
  };
  metadata: {
    timestamp: string;
    requestId: string;
  };
}
```

### Integration Points

#### Frontend Context Integration
The existing React contexts will be updated to use API calls instead of direct database operations:

```typescript
// AuthContext integration
const login = async (username: string, password: string) => {
  const response = await fetch('/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ username, password })
  });
  
  if (response.ok) {
    const { data } = await response.json();
    localStorage.setItem('authToken', data.token);
    setIsAuthenticated(true);
  } else {
    throw new Error('Authentication failed');
  }
};

// TaskContext integration  
const addTask = async (taskData: CreateTaskRequest) => {
  const response = await fetch('/api/tasks', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`
    },
    body: JSON.stringify(taskData)
  });
  
  if (response.ok) {
    const { data } = await response.json();
    setTasks(prev => [...prev, data]);
    return data;
  } else {
    throw new Error('Failed to create task');
  }
};
```

#### Weather Service Integration
The existing weather service integration will remain unchanged on the frontend, maintaining the current API integration pattern.

### Deployment Considerations

#### Environment Configuration
```typescript
interface Config {
  port: number;
  database: {
    path: string;
    backupPath: string;
    vacuumInterval: number;
  };
  auth: {
    jwtSecret: string;
    jwtExpiry: string;
    bcryptSaltRounds: number;
    sessionTimeout: number;
  };
  security: {
    rateLimitWindow: number;
    rateLimitMax: number;
    corsOrigins: string[];
  };
}
```

#### Production Readiness Checklist
- [ ] Environment-based configuration
- [ ] Secure JWT secret generation
- [ ] Database file permissions and backup strategy
- [ ] Process management (PM2 or systemd)
- [ ] Logging configuration
- [ ] Health check endpoints
- [ ] Error monitoring integration
- [ ] Performance monitoring setup

This backend architecture preserves all existing frontend functionality while providing a secure, scalable, and maintainable foundation for 30-year operational reliability.