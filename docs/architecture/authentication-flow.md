# LifeTracker Authentication Flow and Security Implementation

## Executive Summary

This document defines the comprehensive authentication and security architecture for LifeTracker's production deployment. The design replaces hardcoded credentials with secure bcrypt hashing, implements JWT-based session management with 5-minute auto-logout, and includes comprehensive security measures including rate limiting, input validation, and session protection.

## Authentication Architecture

### Current State Analysis
**Existing Implementation Issues:**
- Hardcoded credentials: `username: 's<PERSON><PERSON><PERSON>'`, `password: 'Lookup88?'`
- Simple localStorage boolean flag for authentication state
- No password security (plain text comparison)
- No session timeout or management
- No protection against brute force attacks
- Direct URL access bypass vulnerability

**Target Security Model:**
- Bcrypt-hashed password storage with salt rounds
- JWT-based authentication with short expiration (5 minutes)
- Automatic logout on inactivity and browser/tab close
- Rate limiting on authentication endpoints
- Secure session management with token refresh capability
- Protection against common web vulnerabilities

## Authentication Flow Design

### High-Level Authentication Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    Authentication Flow                         │
├─────────────────────────────────────────────────────────────────┤
│  1. User Login Request                                          │
│     ├── Username/Password Input                                 │
│     ├── Client-Side Validation                                  │
│     └── POST /api/auth/login                                    │
├─────────────────────────────────────────────────────────────────┤
│  2. Server-Side Authentication                                  │
│     ├── Rate Limit Check                                        │
│     ├── Input Validation (<PERSON><PERSON>)                           │
│     ├── Database Credential Lookup                              │
│     ├── Bcrypt Password Verification                            │
│     ├── JWT Token Generation                                    │
│     └── Session Creation                                        │
├─────────────────────────────────────────────────────────────────┤
│  3. Token Management                                            │
│     ├── 5-minute Token Expiration                               │
│     ├── Automatic Token Refresh                                 │
│     ├── Inactivity Detection                                    │
│     └── Browser/Tab Close Handling                              │
├─────────────────────────────────────────────────────────────────┤
│  4. Protected Route Access                                      │
│     ├── JWT Token Validation                                    │
│     ├── Token Expiration Check                                  │
│     ├── User Session Verification                               │
│     └── Request Authorization                                   │
├─────────────────────────────────────────────────────────────────┤
│  5. Logout and Session Cleanup                                  │
│     ├── Token Invalidation                                      │
│     ├── Session Cleanup                                         │
│     ├── Local Storage Clearing                                  │
│     └── Redirect to Login                                       │
└─────────────────────────────────────────────────────────────────┘
```

## Detailed Authentication Implementation

### 1. Password Security

#### Bcrypt Configuration
```typescript
interface PasswordConfig {
  saltRounds: 12; // High security, ~300ms on modern hardware
  algorithm: 'bcrypt'; // Industry standard for password hashing
  minLength: 8; // Minimum password length
  maxLength: 128; // Maximum password length for bcrypt
}

// Password hashing implementation
const hashPassword = async (password: string): Promise<string> => {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
};

// Password verification implementation
const verifyPassword = async (password: string, hash: string): Promise<boolean> => {
  return await bcrypt.compare(password, hash);
};
```

#### Initial Credential Setup
```typescript
// Migration script to hash existing password
const migrateCredentials = async () => {
  const existingPassword = 'Lookup88?'; // Current hardcoded password
  const hashedPassword = await hashPassword(existingPassword);
  
  await db.run(
    'UPDATE user_credentials SET password_hash = ? WHERE username = ?',
    [hashedPassword, 'scharway']
  );
  
  console.log('Credentials migrated successfully');
};
```

### 2. JWT Token Management

#### JWT Configuration
```typescript
interface JWTConfig {
  secret: string; // 256-bit secret from environment
  algorithm: 'HS256'; // HMAC with SHA-256
  expiresIn: '5m'; // 5-minute expiration
  issuer: 'lifetracker-api';
  audience: 'lifetracker-client';
}

interface JWTPayload {
  sub: string; // User ID (subject)
  username: string; // Username
  iat: number; // Issued at timestamp
  exp: number; // Expiration timestamp
  jti: string; // Unique token ID for revocation
}
```

#### Token Generation
```typescript
const generateToken = (user: User): string => {
  const payload: JWTPayload = {
    sub: user.id.toString(),
    username: user.username,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (5 * 60), // 5 minutes
    jti: crypto.randomUUID() // Unique token ID
  };
  
  return jwt.sign(payload, config.jwt.secret, {
    algorithm: 'HS256',
    issuer: config.jwt.issuer,
    audience: config.jwt.audience
  });
};
```

#### Token Validation
```typescript
const validateToken = (token: string): JWTPayload => {
  try {
    const decoded = jwt.verify(token, config.jwt.secret, {
      algorithms: ['HS256'],
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    }) as JWTPayload;
    
    // Check if token is in revocation list (future enhancement)
    if (isTokenRevoked(decoded.jti)) {
      throw new Error('Token has been revoked');
    }
    
    return decoded;
  } catch (error) {
    throw new Error('Invalid or expired token');
  }
};
```

### 3. Session Management

#### Session Store Design
```typescript
interface Session {
  tokenId: string; // JWT token ID (jti)
  userId: number; // User ID
  createdAt: Date; // Session creation time
  lastActivity: Date; // Last request timestamp
  ipAddress: string; // Client IP address
  userAgent: string; // Client user agent
  isActive: boolean; // Session status
}

// In-memory session store (Redis alternative for future)
class SessionStore {
  private sessions = new Map<string, Session>();
  
  createSession(tokenId: string, userId: number, req: Request): void {
    const session: Session = {
      tokenId,
      userId,
      createdAt: new Date(),
      lastActivity: new Date(),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent') || '',
      isActive: true
    };
    
    this.sessions.set(tokenId, session);
  }
  
  updateActivity(tokenId: string): boolean {
    const session = this.sessions.get(tokenId);
    if (session && session.isActive) {
      session.lastActivity = new Date();
      return true;
    }
    return false;
  }
  
  invalidateSession(tokenId: string): void {
    const session = this.sessions.get(tokenId);
    if (session) {
      session.isActive = false;
      this.sessions.delete(tokenId);
    }
  }
  
  isSessionValid(tokenId: string): boolean {
    const session = this.sessions.get(tokenId);
    return session?.isActive === true;
  }
  
  cleanupExpiredSessions(): void {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60 * 1000);
    
    for (const [tokenId, session] of this.sessions) {
      if (session.lastActivity < fiveMinutesAgo) {
        this.invalidateSession(tokenId);
      }
    }
  }
}
```

### 4. Authentication Middleware

#### JWT Authentication Middleware
```typescript
const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_REQUIRED',
          message: 'Authentication token required'
        }
      });
    }
    
    const token = authHeader.substring(7); // Remove "Bearer " prefix
    
    // Validate JWT token
    const payload = validateToken(token);
    
    // Check session validity
    if (!sessionStore.isSessionValid(payload.jti)) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_SESSION_INVALID',
          message: 'Session is no longer valid'
        }
      });
    }
    
    // Update session activity
    sessionStore.updateActivity(payload.jti);
    
    // Add user information to request
    req.user = {
      id: parseInt(payload.sub),
      username: payload.username,
      tokenId: payload.jti
    };
    
    next();
  } catch (error) {
    return res.status(401).json({
      success: false,
      error: {
        code: 'AUTH_TOKEN_INVALID',
        message: 'Invalid or expired authentication token'
      }
    });
  }
};
```

### 5. Rate Limiting

#### Authentication Rate Limiting
```typescript
interface RateLimitConfig {
  login: {
    windowMs: 15 * 60 * 1000; // 15 minutes
    max: 5; // 5 attempts per window
    message: 'Too many login attempts, please try again later';
    standardHeaders: true;
    legacyHeaders: false;
  };
  general: {
    windowMs: 15 * 60 * 1000; // 15 minutes
    max: 1000; // 1000 requests per window
    standardHeaders: true;
    legacyHeaders: false;
  };
}

// Rate limiter implementation
const createRateLimiter = (options: RateLimitOptions) => {
  return rateLimit({
    ...options,
    handler: (req: Request, res: Response) => {
      res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: options.message,
          details: {
            limit: options.max,
            remaining: 0,
            resetTime: new Date(Date.now() + options.windowMs).toISOString()
          }
        }
      });
    }
  });
};

// Apply rate limiters
app.use('/api/auth/login', createRateLimiter(config.rateLimit.login));
app.use('/api', createRateLimiter(config.rateLimit.general));
```

#### Advanced Rate Limiting (Account Lockout)
```typescript
interface AccountLockout {
  maxAttempts: 5; // Lock account after 5 failed attempts
  lockoutDuration: 30 * 60 * 1000; // 30 minutes lockout
  resetAttempts: 24 * 60 * 60 * 1000; // Reset counter after 24 hours
}

const checkAccountLockout = async (username: string): Promise<boolean> => {
  const user = await db.get(
    'SELECT login_attempts, locked_until FROM user_credentials WHERE username = ?',
    [username]
  );
  
  if (!user) return false;
  
  // Check if account is currently locked
  if (user.locked_until && new Date(user.locked_until) > new Date()) {
    return true; // Account is locked
  }
  
  // Reset lockout if time has passed
  if (user.locked_until && new Date(user.locked_until) <= new Date()) {
    await db.run(
      'UPDATE user_credentials SET login_attempts = 0, locked_until = NULL WHERE username = ?',
      [username]
    );
  }
  
  return false; // Account is not locked
};

const recordFailedLogin = async (username: string): Promise<void> => {
  const user = await db.get(
    'SELECT login_attempts FROM user_credentials WHERE username = ?',
    [username]
  );
  
  if (!user) return;
  
  const newAttempts = (user.login_attempts || 0) + 1;
  let lockedUntil = null;
  
  if (newAttempts >= 5) {
    lockedUntil = new Date(Date.now() + 30 * 60 * 1000).toISOString(); // 30 minutes
  }
  
  await db.run(
    'UPDATE user_credentials SET login_attempts = ?, locked_until = ? WHERE username = ?',
    [newAttempts, lockedUntil, username]
  );
};

const recordSuccessfulLogin = async (username: string): Promise<void> => {
  await db.run(
    'UPDATE user_credentials SET login_attempts = 0, locked_until = NULL, last_login = ? WHERE username = ?',
    [new Date().toISOString(), username]
  );
};
```

## Frontend Integration

### 1. Login Process Update

#### Enhanced AuthContext Implementation
```typescript
// Updated AuthContext for backend integration
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isInitializing, setIsInitializing] = useState<boolean>(true);
  const [user, setUser] = useState<User | null>(null);
  
  // Token refresh timer
  const tokenRefreshTimer = useRef<NodeJS.Timeout | null>(null);
  
  useEffect(() => {
    checkExistingSession();
    setupInactivityHandlers();
    
    return () => {
      if (tokenRefreshTimer.current) {
        clearInterval(tokenRefreshTimer.current);
      }
    };
  }, []);
  
  const checkExistingSession = async () => {
    const token = localStorage.getItem('authToken');
    if (token) {
      try {
        const response = await fetch('/api/auth/session', {
          headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (response.ok) {
          const { data } = await response.json();
          setIsAuthenticated(true);
          setUser(data.user);
          setupTokenRefresh(token);
        } else {
          localStorage.removeItem('authToken');
        }
      } catch (error) {
        localStorage.removeItem('authToken');
      }
    }
    setIsInitializing(false);
  };
  
  const login = async (username: string, password: string): Promise<void> => {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ username, password })
    });
    
    if (response.ok) {
      const { data } = await response.json();
      localStorage.setItem('authToken', data.token);
      setIsAuthenticated(true);
      setUser(data.user);
      setupTokenRefresh(data.token);
    } else {
      const { error } = await response.json();
      throw new Error(error.message);
    }
  };
  
  const logout = async (): Promise<void> => {
    const token = localStorage.getItem('authToken');
    if (token) {
      try {
        await fetch('/api/auth/logout', {
          method: 'POST',
          headers: { 'Authorization': `Bearer ${token}` }
        });
      } catch (error) {
        // Continue with logout even if server call fails
      }
    }
    
    localStorage.removeItem('authToken');
    setIsAuthenticated(false);
    setUser(null);
    
    if (tokenRefreshTimer.current) {
      clearInterval(tokenRefreshTimer.current);
    }
  };
  
  const setupTokenRefresh = (token: string) => {
    // Refresh token every 4 minutes (before 5-minute expiration)
    tokenRefreshTimer.current = setInterval(async () => {
      try {
        const response = await fetch('/api/auth/refresh', {
          method: 'POST',
          headers: { 'Authorization': `Bearer ${token}` }
        });
        
        if (response.ok) {
          const { data } = await response.json();
          localStorage.setItem('authToken', data.token);
        } else {
          await logout(); // Token refresh failed, logout user
        }
      } catch (error) {
        await logout(); // Network error, logout user
      }
    }, 4 * 60 * 1000); // 4 minutes
  };
  
  const setupInactivityHandlers = () => {
    let lastActivity = Date.now();
    
    const resetActivityTimer = () => {
      lastActivity = Date.now();
    };
    
    // Monitor user activity
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    events.forEach(event => {
      document.addEventListener(event, resetActivityTimer);
    });
    
    // Check for inactivity every minute
    const inactivityChecker = setInterval(() => {
      if (Date.now() - lastActivity > 5 * 60 * 1000) { // 5 minutes
        logout();
      }
    }, 60 * 1000);
    
    // Handle page unload (browser/tab close)
    const handleBeforeUnload = () => {
      // Clear authentication state but keep token for potential refresh
      setIsAuthenticated(false);
    };
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    // Cleanup event listeners
    return () => {
      events.forEach(event => {
        document.removeEventListener(event, resetActivityTimer);
      });
      clearInterval(inactivityChecker);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  };
  
  return (
    <AuthContext.Provider value={{ isAuthenticated, isInitializing, user, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};
```

### 2. API Request Interceptor

#### Automatic Token Handling
```typescript
// Utility for making authenticated API calls
const apiCall = async (url: string, options: RequestInit = {}): Promise<any> => {
  const token = localStorage.getItem('authToken');
  
  const config: RequestInit = {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
  };
  
  const response = await fetch(url, config);
  
  // Handle authentication errors
  if (response.status === 401) {
    localStorage.removeItem('authToken');
    window.location.href = '/login';
    throw new Error('Authentication required');
  }
  
  // Handle rate limiting
  if (response.status === 429) {
    const result = await response.json();
    throw new Error(result.error.message);
  }
  
  const result = await response.json();
  
  if (!response.ok) {
    throw new Error(result.error.message);
  }
  
  return result;
};
```

### 3. Route Protection

#### Enhanced Route Guards
```typescript
// Updated ProtectedRoute component
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated, isInitializing } = useAuth();
  const location = useLocation();
  
  useEffect(() => {
    // Prevent login page bypass via direct URL access
    if (!isAuthenticated && !isInitializing) {
      // Store attempted URL for redirect after login
      sessionStorage.setItem('redirectUrl', location.pathname);
    }
  }, [isAuthenticated, isInitializing, location.pathname]);
  
  if (isInitializing) {
    return <div>Loading...</div>; // Or your loading component
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

// Updated login redirect handling
const LoginPage: React.FC = () => {
  const { login } = useAuth();
  const navigate = useNavigate();
  
  const handleLogin = async (username: string, password: string) => {
    try {
      await login(username, password);
      
      // Redirect to originally requested URL or dashboard
      const redirectUrl = sessionStorage.getItem('redirectUrl') || '/';
      sessionStorage.removeItem('redirectUrl');
      navigate(redirectUrl, { replace: true });
    } catch (error) {
      // Handle login error
      setError(error.message);
    }
  };
  
  // ... rest of login component
};
```

## Security Hardening

### 1. Input Validation

#### Comprehensive Validation Schema
```typescript
const loginSchema = Joi.object({
  username: Joi.string()
    .min(3)
    .max(50)
    .pattern(/^[a-zA-Z0-9_]+$/) // Alphanumeric and underscore only
    .required()
    .messages({
      'string.pattern.base': 'Username can only contain letters, numbers, and underscores',
      'string.min': 'Username must be at least 3 characters',
      'string.max': 'Username cannot exceed 50 characters'
    }),
  password: Joi.string()
    .min(8)
    .max(128)
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters',
      'string.max': 'Password cannot exceed 128 characters'
    })
});

// Validation middleware
const validateLogin = (req: Request, res: Response, next: NextFunction) => {
  const { error } = loginSchema.validate(req.body);
  if (error) {
    return res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: error.details[0].message,
        field: error.details[0].path[0]
      }
    });
  }
  next();
};
```

### 2. Security Headers

#### Helmet.js Configuration
```typescript
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.open-meteo.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for development
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));
```

### 3. CORS Configuration

#### Secure CORS Setup
```typescript
const corsOptions: CorsOptions = {
  origin: (origin, callback) => {
    const allowedOrigins = [
      'http://localhost:3000', // React dev server
      'http://localhost:5173', // Vite dev server
      // Add production domains here
    ];
    
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true, // Allow cookies and auth headers
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID'],
  maxAge: 86400 // 24 hours preflight cache
};

app.use(cors(corsOptions));
```

This authentication architecture provides comprehensive security while maintaining the existing user experience and ensuring robust protection against common web application vulnerabilities.