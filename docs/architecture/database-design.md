# LifeTracker Database Design

## Executive Summary

This document defines the comprehensive database design for LifeTracker's production backend using SQLite with better-sqlite3. The design preserves the existing schema structure while adding performance optimizations, data integrity constraints, and maintenance procedures for 30-year operational reliability.

## Database Technology Selection

### SQLite with better-sqlite3
**Rationale for SQLite:**
- **Proven Reliability**: SQLite is one of the most widely deployed database engines
- **ACID Compliance**: Full transaction support with rollback capabilities
- **Single-User Application**: Perfect fit for LifeTracker's single-user model
- **File-Based Storage**: Simple backup and migration strategies
- **Zero Configuration**: No database server administration required
- **Cross-Platform**: Runs on any platform Node.js supports

**better-sqlite3 Advantages:**
- **Synchronous Operations**: Better performance than asynchronous sqlite3
- **Prepared Statements**: Built-in SQL injection protection
- **Transaction Support**: Explicit transaction control
- **Memory Efficiency**: Lower memory footprint
- **Node.js Native**: C++ bindings for optimal performance

### Database Configuration
```typescript
interface DatabaseConfig {
  filepath: string; // Path to SQLite database file
  options: {
    readonly: boolean; // false for read-write access
    fileMustExist: boolean; // false to create if not exists
    timeout: number; // Connection timeout (5000ms)
    verbose: Function | null; // Logging function for development
  };
  pragma: {
    journal_mode: 'WAL'; // Write-Ahead Logging for better concurrency
    synchronous: 'NORMAL'; // Balance between safety and performance
    cache_size: number; // Memory cache size (-64000 = 64MB)
    temp_store: 'MEMORY'; // Store temporary tables in memory
    mmap_size: number; // Memory-mapped I/O size (268435456 = 256MB)
    optimize: boolean; // Run PRAGMA optimize on close
  };
}
```

## Schema Design

### Core Tables

#### Tasks Table
```sql
CREATE TABLE tasks (
    -- Primary identification
    id TEXT PRIMARY KEY NOT NULL,
    
    -- Basic task information
    title TEXT NOT NULL CHECK (length(title) > 0 AND length(title) <= 200),
    description TEXT CHECK (description IS NULL OR length(description) <= 1000),
    
    -- Scheduling information
    dueDate TEXT NOT NULL CHECK (date(dueDate) IS NOT NULL),
    dueTime TEXT NOT NULL CHECK (time(dueTime) IS NOT NULL),
    
    -- Status tracking
    isCompleted INTEGER NOT NULL DEFAULT 0 CHECK (isCompleted IN (0, 1)),
    completedAt TEXT CHECK (completedAt IS NULL OR datetime(completedAt) IS NOT NULL),
    
    -- Task attributes
    priority TEXT NOT NULL CHECK (priority IN ('Low', 'Medium', 'High')),
    category TEXT CHECK (category IS NULL OR (length(category) > 0 AND length(category) <= 50)),
    
    -- Recurrence configuration
    isRecurring INTEGER NOT NULL DEFAULT 0 CHECK (isRecurring IN (0, 1)),
    recurrenceType TEXT CHECK (
        recurrenceType IS NULL OR 
        recurrenceType IN ('Daily', 'Weekly', 'Monthly', 'Yearly')
    ),
    recurrenceInterval INTEGER CHECK (
        recurrenceInterval IS NULL OR 
        (recurrenceInterval >= 1 AND recurrenceInterval <= 365)
    ),
    
    -- Audit timestamps
    createdAt TEXT NOT NULL DEFAULT (datetime('now')) 
        CHECK (datetime(createdAt) IS NOT NULL),
    updatedAt TEXT NOT NULL DEFAULT (datetime('now')) 
        CHECK (datetime(updatedAt) IS NOT NULL),
    
    -- Constraints
    CONSTRAINT valid_recurrence_config CHECK (
        (isRecurring = 0 AND recurrenceType IS NULL AND recurrenceInterval IS NULL) OR
        (isRecurring = 1 AND recurrenceType IS NOT NULL)
    ),
    CONSTRAINT valid_completion CHECK (
        (isCompleted = 0 AND completedAt IS NULL) OR
        (isCompleted = 1 AND completedAt IS NOT NULL)
    )
);
```

#### Settings Table
```sql
CREATE TABLE settings (
    -- Primary identification
    key TEXT PRIMARY KEY NOT NULL CHECK (length(key) > 0 AND length(key) <= 50),
    
    -- Setting value
    value TEXT NOT NULL,
    
    -- Audit timestamp
    updatedAt TEXT NOT NULL DEFAULT (datetime('now')) 
        CHECK (datetime(updatedAt) IS NOT NULL),
    
    -- Predefined setting keys
    CONSTRAINT valid_setting_keys CHECK (
        key IN ('showCompleted', 'seeded', 'theme', 'language', 'timezone')
    )
);
```

#### User Credentials Table
```sql
CREATE TABLE user_credentials (
    -- Single user constraint
    id INTEGER PRIMARY KEY CHECK (id = 1),
    
    -- Authentication data
    username TEXT NOT NULL UNIQUE CHECK (length(username) >= 3 AND length(username) <= 50),
    password_hash TEXT NOT NULL CHECK (length(password_hash) = 60), -- Bcrypt hash length
    
    -- Session management
    last_login TEXT CHECK (last_login IS NULL OR datetime(last_login) IS NOT NULL),
    login_attempts INTEGER NOT NULL DEFAULT 0 CHECK (login_attempts >= 0),
    locked_until TEXT CHECK (locked_until IS NULL OR datetime(locked_until) IS NOT NULL),
    
    -- Audit timestamps
    created_at TEXT NOT NULL DEFAULT (datetime('now')) 
        CHECK (datetime(created_at) IS NOT NULL),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')) 
        CHECK (datetime(updated_at) IS NOT NULL)
);
```

#### Database Metadata Table
```sql
CREATE TABLE database_metadata (
    -- Single row constraint
    id INTEGER PRIMARY KEY CHECK (id = 1),
    
    -- Schema versioning
    schema_version TEXT NOT NULL DEFAULT '1.0.0',
    
    -- Maintenance tracking
    last_vacuum TEXT CHECK (last_vacuum IS NULL OR datetime(last_vacuum) IS NOT NULL),
    last_analyze TEXT CHECK (last_analyze IS NULL OR datetime(last_analyze) IS NOT NULL),
    last_integrity_check TEXT CHECK (last_integrity_check IS NULL OR datetime(last_integrity_check) IS NOT NULL),
    
    -- Statistics
    total_tasks_created INTEGER NOT NULL DEFAULT 0 CHECK (total_tasks_created >= 0),
    total_tasks_completed INTEGER NOT NULL DEFAULT 0 CHECK (total_tasks_completed >= 0),
    
    -- Audit timestamps
    created_at TEXT NOT NULL DEFAULT (datetime('now')) 
        CHECK (datetime(created_at) IS NOT NULL),
    updated_at TEXT NOT NULL DEFAULT (datetime('now')) 
        CHECK (datetime(updated_at) IS NOT NULL)
);
```

### Performance Indexes

#### Tasks Table Indexes
```sql
-- Primary query patterns
CREATE INDEX idx_tasks_due_date ON tasks(dueDate);
CREATE INDEX idx_tasks_completed ON tasks(isCompleted);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_category ON tasks(category) WHERE category IS NOT NULL;
CREATE INDEX idx_tasks_recurring ON tasks(isRecurring) WHERE isRecurring = 1;

-- Composite indexes for common query combinations
CREATE INDEX idx_tasks_due_completed ON tasks(dueDate, isCompleted);
CREATE INDEX idx_tasks_category_completed ON tasks(category, isCompleted) WHERE category IS NOT NULL;
CREATE INDEX idx_tasks_priority_due ON tasks(priority, dueDate);

-- Full-text search index for title and description
CREATE INDEX idx_tasks_title ON tasks(title);

-- Audit and maintenance indexes
CREATE INDEX idx_tasks_created_at ON tasks(createdAt);
CREATE INDEX idx_tasks_updated_at ON tasks(updatedAt);
```

#### Settings Table Indexes
```sql
-- Primary access pattern
CREATE INDEX idx_settings_updated_at ON settings(updatedAt);
```

#### User Credentials Indexes
```sql
-- Authentication lookup
CREATE UNIQUE INDEX idx_user_username ON user_credentials(username);
CREATE INDEX idx_user_last_login ON user_credentials(last_login);
```

### Database Triggers

#### Update Timestamp Triggers
```sql
-- Automatically update updatedAt timestamp for tasks
CREATE TRIGGER trigger_tasks_updated_at
    AFTER UPDATE ON tasks
    FOR EACH ROW
BEGIN
    UPDATE tasks SET updatedAt = datetime('now') WHERE id = NEW.id;
END;

-- Automatically update updatedAt timestamp for settings
CREATE TRIGGER trigger_settings_updated_at
    AFTER UPDATE ON settings
    FOR EACH ROW
BEGIN
    UPDATE settings SET updatedAt = datetime('now') WHERE key = NEW.key;
END;

-- Automatically update updatedAt timestamp for user_credentials
CREATE TRIGGER trigger_user_updated_at
    AFTER UPDATE ON user_credentials
    FOR EACH ROW
BEGIN
    UPDATE user_credentials SET updated_at = datetime('now') WHERE id = NEW.id;
END;
```

#### Statistics Tracking Triggers
```sql
-- Track task creation statistics
CREATE TRIGGER trigger_task_created_stats
    AFTER INSERT ON tasks
    FOR EACH ROW
BEGIN
    UPDATE database_metadata 
    SET total_tasks_created = total_tasks_created + 1,
        updated_at = datetime('now')
    WHERE id = 1;
END;

-- Track task completion statistics
CREATE TRIGGER trigger_task_completed_stats
    AFTER UPDATE OF isCompleted ON tasks
    FOR EACH ROW
    WHEN NEW.isCompleted = 1 AND OLD.isCompleted = 0
BEGIN
    UPDATE database_metadata 
    SET total_tasks_completed = total_tasks_completed + 1,
        updated_at = datetime('now')
    WHERE id = 1;
END;
```

#### Data Validation Triggers
```sql
-- Validate recurring task configuration
CREATE TRIGGER trigger_validate_recurring_task
    BEFORE INSERT ON tasks
    FOR EACH ROW
    WHEN NEW.isRecurring = 1
BEGIN
    SELECT CASE
        WHEN NEW.recurrenceType IS NULL THEN
            RAISE(ABORT, 'Recurring tasks must have a recurrence type')
        WHEN NEW.recurrenceInterval IS NULL THEN
            RAISE(ABORT, 'Recurring tasks must have a recurrence interval')
        WHEN NEW.recurrenceInterval < 1 OR NEW.recurrenceInterval > 365 THEN
            RAISE(ABORT, 'Recurrence interval must be between 1 and 365')
    END;
END;
```

## Data Migration Strategy

### Schema Initialization
```sql
-- Initialize database with default data
INSERT OR IGNORE INTO database_metadata (id, schema_version) VALUES (1, '1.0.0');

INSERT OR IGNORE INTO settings (key, value) VALUES 
    ('showCompleted', 'true'),
    ('seeded', 'false'),
    ('theme', 'light'),
    ('language', 'en'),
    ('timezone', 'America/Chicago');

-- Initialize user credentials (will be updated via API)
INSERT OR IGNORE INTO user_credentials (id, username, password_hash) 
VALUES (1, 'scharway', '$2b$12$placeholder_hash_to_be_updated');
```

### Migration Framework
```typescript
interface Migration {
  version: string;
  description: string;
  up: string; // SQL for applying migration
  down: string; // SQL for reverting migration
}

const migrations: Migration[] = [
  {
    version: '1.0.0',
    description: 'Initial schema creation',
    up: `
      -- Create tables, indexes, and triggers
      -- (Full schema creation SQL)
    `,
    down: `
      DROP TABLE IF EXISTS tasks;
      DROP TABLE IF EXISTS settings;
      DROP TABLE IF EXISTS user_credentials;
      DROP TABLE IF EXISTS database_metadata;
    `
  }
];
```

## Data Integrity and Constraints

### Primary Key Strategy
- **Tasks**: UUID v4 generated by `crypto.randomUUID()`
- **Settings**: Natural key (setting name)
- **User Credentials**: Single row with id = 1
- **Database Metadata**: Single row with id = 1

### Foreign Key Constraints
SQLite foreign key constraints are not used in this design as all tables are independent entities. This simplifies the schema while maintaining data integrity through application logic.

### Check Constraints
- **String Lengths**: All text fields have appropriate length limits
- **Date Formats**: Date and time fields validated against SQLite date/time functions
- **Enum Values**: Priority and recurrence types restricted to valid values
- **Boolean Fields**: Stored as INTEGER with CHECK constraints for 0/1 values
- **Business Rules**: Complex constraints for recurring task configuration

### Data Validation Rules
```typescript
interface ValidationRules {
  tasks: {
    title: { required: true; minLength: 1; maxLength: 200 };
    description: { required: false; maxLength: 1000 };
    dueDate: { required: true; format: 'YYYY-MM-DD' };
    dueTime: { required: true; format: 'HH:mm' };
    priority: { required: true; enum: ['Low', 'Medium', 'High'] };
    category: { required: false; maxLength: 50 };
    recurrenceType: { required: false; enum: ['Daily', 'Weekly', 'Monthly', 'Yearly'] };
    recurrenceInterval: { required: false; min: 1; max: 365 };
  };
  settings: {
    key: { required: true; maxLength: 50; enum: ['showCompleted', 'seeded', 'theme', 'language', 'timezone'] };
    value: { required: true; maxLength: 1000 };
  };
  userCredentials: {
    username: { required: true; minLength: 3; maxLength: 50 };
    passwordHash: { required: true; length: 60 }; // Bcrypt hash
  };
}
```

## Performance Optimization

### Query Optimization Strategies
1. **Index Coverage**: All common query patterns covered by indexes
2. **Query Planning**: Use EXPLAIN QUERY PLAN to optimize complex queries
3. **Prepared Statements**: All queries use prepared statements for performance and security
4. **Connection Reuse**: Single persistent database connection
5. **Memory Settings**: Optimized cache_size and mmap_size for performance

### Common Query Patterns
```sql
-- Get all incomplete tasks (most common query)
SELECT * FROM tasks 
WHERE isCompleted = 0 
ORDER BY dueDate, dueTime;

-- Get tasks by category and completion status
SELECT * FROM tasks 
WHERE category = ? AND isCompleted = ? 
ORDER BY dueDate;

-- Get overdue tasks
SELECT * FROM tasks 
WHERE isCompleted = 0 
  AND date(dueDate) < date('now')
ORDER BY dueDate;

-- Search tasks by title/description
SELECT * FROM tasks 
WHERE (title LIKE '%' || ? || '%' OR description LIKE '%' || ? || '%')
  AND isCompleted = ?
ORDER BY dueDate;

-- Get recurring tasks due for next occurrence calculation
SELECT * FROM tasks 
WHERE isRecurring = 1 
  AND isCompleted = 1 
  AND datetime(completedAt) >= datetime('now', '-1 day');
```

### Performance Monitoring
```typescript
interface QueryMetrics {
  query: string;
  executionTime: number;
  rowsAffected: number;
  timestamp: string;
}

// Performance tracking in database service
const trackQuery = (sql: string, params: any[], startTime: number) => {
  const executionTime = Date.now() - startTime;
  if (executionTime > 100) { // Log slow queries
    console.warn(`Slow query detected: ${executionTime}ms`, { sql, params });
  }
};
```

## Database Maintenance

### Automated Maintenance Schedule
```typescript
interface MaintenanceSchedule {
  vacuum: {
    frequency: 'weekly'; // Every Sunday at 2 AM
    command: 'VACUUM';
    description: 'Reclaim disk space and defragment database';
  };
  analyze: {
    frequency: 'daily'; // Every day at 3 AM
    command: 'ANALYZE';
    description: 'Update query planner statistics';
  };
  integrityCheck: {
    frequency: 'weekly'; // Every Sunday at 4 AM
    command: 'PRAGMA integrity_check';
    description: 'Verify database integrity';
  };
  optimize: {
    frequency: 'daily'; // Before each backup
    command: 'PRAGMA optimize';
    description: 'Optimize database performance';
  };
}
```

### Maintenance Operations
```sql
-- Vacuum operation to reclaim space
VACUUM;

-- Analyze operation to update statistics
ANALYZE;

-- Integrity check
PRAGMA integrity_check;

-- Optimize database
PRAGMA optimize;

-- Quick integrity check
PRAGMA quick_check;

-- Database statistics
SELECT 
    name,
    sql,
    (SELECT COUNT(*) FROM sqlite_master WHERE type='table') as table_count,
    (SELECT COUNT(*) FROM sqlite_master WHERE type='index') as index_count
FROM sqlite_master 
WHERE type='table';
```

### Backup Strategy
```typescript
interface BackupStrategy {
  frequency: 'daily'; // Daily at 1 AM
  retention: {
    daily: 7; // Keep 7 daily backups
    weekly: 4; // Keep 4 weekly backups
    monthly: 12; // Keep 12 monthly backups
  };
  format: 'file_copy'; // Simple file copy for SQLite
  verification: boolean; // Verify backup integrity
  compression: boolean; // Compress backup files
}

// Backup implementation
const createBackup = async (): Promise<string> => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupPath = `${config.backup.path}/lifetracker_${timestamp}.db`;
  
  // Use SQLite VACUUM INTO for consistent backup
  db.exec(`VACUUM INTO '${backupPath}'`);
  
  // Verify backup integrity
  const backupDb = new Database(backupPath, { readonly: true });
  const result = backupDb.pragma('integrity_check');
  backupDb.close();
  
  if (result[0].integrity_check !== 'ok') {
    throw new Error('Backup integrity check failed');
  }
  
  return backupPath;
};
```

## Data Recovery Procedures

### Recovery Scenarios
1. **Database Corruption**: Restore from latest verified backup
2. **Accidental Data Deletion**: Use backup to restore specific records
3. **Schema Migration Failure**: Rollback to previous schema version
4. **File System Issues**: Restore database file from backup

### Recovery Implementation
```typescript
const recoverDatabase = async (backupPath: string): Promise<void> => {
  // Verify backup before recovery
  const backupDb = new Database(backupPath, { readonly: true });
  const integrity = backupDb.pragma('integrity_check');
  backupDb.close();
  
  if (integrity[0].integrity_check !== 'ok') {
    throw new Error('Backup file is corrupted');
  }
  
  // Close current database connection
  db.close();
  
  // Replace current database with backup
  await fs.copyFile(backupPath, config.database.path);
  
  // Reinitialize database connection
  await initializeDatabase();
  
  console.log('Database recovered successfully from backup');
};
```

## Security Considerations

### Data Protection
- **At Rest**: Database file permissions set to owner read/write only (600)
- **Backup Encryption**: Consider encrypting backup files (future enhancement)
- **Access Control**: Single-user application limits exposure
- **SQL Injection**: Prevented by prepared statements and validation

### Audit Trail
```sql
-- Audit log table (future enhancement)
CREATE TABLE audit_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name TEXT NOT NULL,
    operation TEXT NOT NULL CHECK (operation IN ('INSERT', 'UPDATE', 'DELETE')),
    record_id TEXT NOT NULL,
    old_values TEXT, -- JSON string
    new_values TEXT, -- JSON string
    timestamp TEXT NOT NULL DEFAULT (datetime('now')),
    user_id INTEGER -- Future multi-user support
);
```

## Monitoring and Alerting

### Database Health Metrics
```typescript
interface DatabaseHealth {
  size: number; // Database file size in bytes
  taskCount: number; // Total number of tasks
  completedTaskCount: number; // Number of completed tasks
  lastBackup: string; // Timestamp of last backup
  lastMaintenance: string; // Timestamp of last maintenance
  integrityStatus: 'ok' | 'corrupted'; // Database integrity
  connectionStatus: 'connected' | 'disconnected'; // Connection status
}
```

### Performance Metrics
```typescript
interface PerformanceMetrics {
  averageQueryTime: number; // Average query execution time
  slowQueryCount: number; // Number of slow queries (>100ms)
  connectionPoolStatus: {
    active: number;
    idle: number;
    pending: number;
  };
  cacheHitRatio: number; // SQLite page cache hit ratio
}
```

This database design provides a robust foundation for LifeTracker's 30-year operational requirement while maintaining compatibility with the existing frontend implementation.