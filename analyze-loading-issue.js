import { chromium } from 'playwright';
import fs from 'fs';

async function analyzeLoadingIssue() {
  console.log('🔍 Analyzing LifeTracker loading issue...');
  
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 500
  });
  
  const context = await browser.newContext({
    recordVideo: {
      dir: './videos/',
      size: { width: 1280, height: 720 }
    }
  });
  
  const page = await context.newPage();
  
  // Track all network requests and timing
  const networkLog = [];
  const consoleLog = [];
  const performanceMetrics = [];
  
  page.on('console', msg => {
    const logEntry = `[${msg.type().toUpperCase()}] ${msg.text()}`;
    console.log(`BROWSER: ${logEntry}`);
    consoleLog.push({
      timestamp: Date.now(),
      type: msg.type(),
      text: msg.text()
    });
  });
  
  page.on('request', request => {
    const logEntry = `REQUEST: ${request.method()} ${request.url()}`;
    console.log(logEntry);
    networkLog.push({
      timestamp: Date.now(),
      type: 'request',
      method: request.method(),
      url: request.url(),
      resourceType: request.resourceType()
    });
  });
  
  page.on('response', response => {
    const logEntry = `RESPONSE: ${response.status()} ${response.url()} (${response.headers()['content-type'] || 'unknown'})`;
    console.log(logEntry);
    networkLog.push({
      timestamp: Date.now(),
      type: 'response',
      status: response.status(),
      url: response.url(),
      contentType: response.headers()['content-type'],
      timing: response.request().timing()
    });
  });
  
  try {
    console.log('📱 Navigating to app...');
    const startTime = Date.now();
    
    await page.goto('http://localhost:5173', { 
      waitUntil: 'networkidle',
      timeout: 30000 
    });
    
    const loadTime = Date.now() - startTime;
    console.log(`⏱️  Initial page load took: ${loadTime}ms`);
    
    // Wait a bit and capture initial state
    await page.waitForTimeout(1000);
    await page.screenshot({ path: 'initial-state.png', fullPage: true });
    
    // Check for loading indicators
    console.log('🔄 Checking for loading indicators...');
    
    let loadingDetected = false;
    let iterations = 0;
    const maxIterations = 20;
    
    while (iterations < maxIterations) {
      // Check for various loading states
      const authSpinner = await page.locator('.animate-spin').first().isVisible().catch(() => false);
      const authLoadingText = await page.locator('text=Loading application...').isVisible().catch(() => false);
      const taskLoadingText = await page.locator('text=Loading your tasks...').isVisible().catch(() => false);
      
      if (authSpinner || authLoadingText || taskLoadingText) {
        loadingDetected = true;
        console.log(`🔄 Loading detected at iteration ${iterations}: spinner=${authSpinner}, authText=${authLoadingText}, taskText=${taskLoadingText}`);
        
        performanceMetrics.push({
          timestamp: Date.now(),
          iteration,
          authSpinner,
          authLoadingText,
          taskLoadingText,
          url: page.url()
        });
      } else {
        console.log(`✅ No loading indicators at iteration ${iterations}`);
      }
      
      // Check what's currently visible
      const loginForm = await page.locator('form').isVisible().catch(() => false);
      const mainLayout = await page.locator('.flex.h-screen').isVisible().catch(() => false);
      
      console.log(`📋 Current state: loginForm=${loginForm}, mainLayout=${mainLayout}`);
      
      if (loginForm && !loadingDetected) {
        console.log('🔑 Login form is ready, attempting login...');
        
        try {
          await page.fill('#username', 'scharway');
          await page.fill('#password', 'Lookup88?');
          
          console.log('🚀 Submitting login form...');
          const loginStartTime = Date.now();
          
          await page.click('button[type="submit"]');
          
          // Monitor post-login loading
          let postLoginIterations = 0;
          while (postLoginIterations < 15) {
            const postLoginSpinner = await page.locator('.animate-spin').isVisible().catch(() => false);
            const taskLoading = await page.locator('text=Loading your tasks...').isVisible().catch(() => false);
            const mainLayoutVisible = await page.locator('.flex.h-screen').isVisible().catch(() => false);
            
            console.log(`🔄 Post-login iteration ${postLoginIterations}: spinner=${postLoginSpinner}, taskLoading=${taskLoading}, mainLayout=${mainLayoutVisible}`);
            
            performanceMetrics.push({
              timestamp: Date.now(),
              iteration: `post-login-${postLoginIterations}`,
              postLoginSpinner,
              taskLoading,
              mainLayoutVisible,
              url: page.url()
            });
            
            if (!postLoginSpinner && !taskLoading && mainLayoutVisible) {
              const loginTime = Date.now() - loginStartTime;
              console.log(`✅ Login completed in ${loginTime}ms`);
              break;
            }
            
            await page.waitForTimeout(1000);
            postLoginIterations++;
          }
          
          break;
        } catch (error) {
          console.error('❌ Login failed:', error.message);
          await page.screenshot({ path: 'login-error.png', fullPage: true });
        }
      }
      
      await page.waitForTimeout(1000);
      iterations++;
    }
    
    // Final screenshot
    await page.screenshot({ path: 'final-analysis-state.png', fullPage: true });
    
    // Get performance metrics from browser
    const performanceData = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0];
      const resources = performance.getEntriesByType('resource');
      
      return {
        navigation: {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          totalTime: navigation.loadEventEnd - navigation.fetchStart
        },
        resources: resources.map(r => ({
          name: r.name,
          duration: r.duration,
          transferSize: r.transferSize,
          type: r.initiatorType
        }))
      };
    });
    
    console.log('📊 Performance Summary:');
    console.log(`- DOM Content Loaded: ${performanceData.navigation.domContentLoaded}ms`);
    console.log(`- Load Complete: ${performanceData.navigation.loadComplete}ms`);
    console.log(`- Total Time: ${performanceData.navigation.totalTime}ms`);
    console.log(`- Loading indicators detected: ${loadingDetected}`);
    
    // Save all data
    const analysisData = {
      loadingDetected,
      performanceMetrics,
      networkLog,
      consoleLog,
      browserPerformance: performanceData,
      summary: {
        totalLoadTime: loadTime,
        domContentLoaded: performanceData.navigation.domContentLoaded,
        loadComplete: performanceData.navigation.loadComplete,
        loadingIndicatorsFound: loadingDetected
      }
    };
    
    fs.writeFileSync('loading-analysis.json', JSON.stringify(analysisData, null, 2));
    console.log('💾 Analysis saved to loading-analysis.json');
    
    return analysisData;
    
  } catch (error) {
    console.error('❌ Analysis failed:', error);
    await page.screenshot({ path: 'analysis-error.png', fullPage: true });
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
}

// Run the analysis
analyzeLoadingIssue()
  .then(data => {
    console.log('\n🎯 ANALYSIS COMPLETE');
    console.log('📹 Video recording saved to videos/ directory');
    console.log('📊 Check loading-analysis.json for detailed metrics');
    
    if (data.loadingDetected) {
      console.log('⚠️  ISSUE CONFIRMED: Extended loading indicators detected');
    } else {
      console.log('✅ No extended loading detected in automated test');
      console.log('💡 The issue might be related to specific timing or user interaction patterns');
    }
  })
  .catch(error => {
    console.error('💥 Analysis failed:', error);
  });
