import React, { useContext } from 'react';
import { AuthContext } from '../contexts/AuthContext';

const SettingsView: React.FC = () => {
    const authContext = useContext(AuthContext);

    if (!authContext) throw new Error("Context not found");

    const { logout } = authContext;

    const handleDeleteAccount = () => {
        if (window.confirm("Are you sure you want to delete your account? This action cannot be undone and all your data will be lost.")) {
            alert("Account deleted. Logging out...");
            logout();
        }
    };

    return (
        <div className="max-w-4xl mx-auto space-y-8">
            <div className="bg-white border border-red-500/20 rounded-lg shadow-sm">
                <div className="p-6">
                     <h2 className="text-xl font-semibold text-red-700">Danger Zone</h2>
                     <p className="mt-1 text-brand-secondary">These actions are permanent and cannot be undone.</p>
                     <div className="mt-6 flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 bg-red-500/5 rounded-lg">
                        <div>
                            <h3 className="font-semibold text-brand-primary">Delete Account</h3>
                            <p className="text-sm text-brand-secondary">Permanently delete your account and all associated data.</p>
                        </div>
                         <button 
                            onClick={handleDeleteAccount} 
                            className="mt-3 sm:mt-0 sm:ml-4 flex-shrink-0 px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 focus:ring-offset-white"
                        >
                            Delete My Account
                         </button>
                     </div>
                </div>
            </div>
        </div>
    );
};

export default SettingsView;