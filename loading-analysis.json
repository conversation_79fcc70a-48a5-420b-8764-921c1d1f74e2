{"loadingDetected": false, "performanceMetrics": [{"timestamp": 1754250062313, "iteration": "post-login-0", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250063325, "iteration": "post-login-1", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250064338, "iteration": "post-login-2", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250065350, "iteration": "post-login-3", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250066363, "iteration": "post-login-4", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250067376, "iteration": "post-login-5", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250068388, "iteration": "post-login-6", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250069403, "iteration": "post-login-7", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250070414, "iteration": "post-login-8", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250071425, "iteration": "post-login-9", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250072436, "iteration": "post-login-10", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250073447, "iteration": "post-login-11", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250074458, "iteration": "post-login-12", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250075469, "iteration": "post-login-13", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}, {"timestamp": 1754250076479, "iteration": "post-login-14", "postLoginSpinner": false, "taskLoading": false, "mainLayoutVisible": false, "url": "http://localhost:5173/"}], "networkLog": [{"timestamp": 1754250058299, "type": "request", "method": "GET", "url": "http://localhost:5173/", "resourceType": "document"}, {"timestamp": 1754250058307, "type": "response", "status": 200, "url": "http://localhost:5173/", "contentType": "text/html", "timing": {"startTime": 1754250058299.397, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.151, "responseStart": 6.707, "responseEnd": 7.859}}, {"timestamp": 1754250058314, "type": "request", "method": "GET", "url": "http://localhost:5173/@vite/client", "resourceType": "script"}, {"timestamp": 1754250058314, "type": "request", "method": "GET", "url": "https://cdn.tailwindcss.com/", "resourceType": "script"}, {"timestamp": 1754250058314, "type": "request", "method": "GET", "url": "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap", "resourceType": "stylesheet"}, {"timestamp": 1754250058314, "type": "request", "method": "GET", "url": "http://localhost:5173/index.css", "resourceType": "stylesheet"}, {"timestamp": 1754250058314, "type": "request", "method": "GET", "url": "http://localhost:5173/index.tsx", "resourceType": "script"}, {"timestamp": 1754250058328, "type": "response", "status": 200, "url": "http://localhost:5173/index.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058311.394, "domainLookupStart": 0.041, "domainLookupEnd": 0.043, "connectStart": 0.043, "secureConnectionStart": -1, "connectEnd": 0.324, "requestStart": 0.467, "responseStart": 16.091, "responseEnd": 17.469}}, {"timestamp": 1754250058329, "type": "response", "status": 200, "url": "http://localhost:5173/@vite/client", "contentType": "text/javascript", "timing": {"startTime": 1754250058310.143, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 1.432, "responseStart": 16.425, "responseEnd": 17.637}}, {"timestamp": 1754250058329, "type": "request", "method": "GET", "url": "http://localhost:5173/node_modules/vite/dist/client/env.mjs", "resourceType": "script"}, {"timestamp": 1754250058330, "type": "response", "status": 200, "url": "http://localhost:5173/index.css", "contentType": "text/html", "timing": {"startTime": 1754250058311.1501, "domainLookupStart": 0.056, "domainLookupEnd": 0.058, "connectStart": 0.058, "secureConnectionStart": -1, "connectEnd": 0.562, "requestStart": 0.664, "responseStart": 19.286, "responseEnd": 20.548}}, {"timestamp": 1754250058331, "type": "response", "status": 200, "url": "http://localhost:5173/node_modules/vite/dist/client/env.mjs", "contentType": "text/javascript", "timing": {"startTime": 1754250058328.213, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.093, "responseStart": 2.689, "responseEnd": 2.938}}, {"timestamp": 1754250058387, "type": "response", "status": 302, "url": "https://cdn.tailwindcss.com/", "timing": {"startTime": 1754250058310.374, "domainLookupStart": 0.114, "domainLookupEnd": 1.273, "connectStart": 1.383, "secureConnectionStart": 18.096, "connectEnd": 42.634, "requestStart": 42.782, "responseStart": 73.09, "responseEnd": 74.612999999772}}, {"timestamp": 1754250058387, "type": "request", "method": "GET", "url": "https://cdn.tailwindcss.com/3.4.17", "resourceType": "script"}, {"timestamp": 1754250058403, "type": "response", "status": 200, "url": "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap", "contentType": "text/css; charset=utf-8", "timing": {"startTime": 1754250058310.8909, "domainLookupStart": 0.115, "domainLookupEnd": 6.11, "connectStart": 6.147, "secureConnectionStart": 22.347, "connectEnd": 52.912, "requestStart": 53.089, "responseStart": 88.688, "responseEnd": 90.46}}, {"timestamp": 1754250058445, "type": "response", "status": 200, "url": "https://cdn.tailwindcss.com/3.4.17", "contentType": "text/javascript", "timing": {"startTime": 1754250058384.883, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.287, "responseStart": 25.895, "responseEnd": 49.253}}, {"timestamp": 1754250058503, "type": "request", "method": "GET", "url": "http://localhost:5173/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=e46e0ed1", "resourceType": "script"}, {"timestamp": 1754250058503, "type": "request", "method": "GET", "url": "http://localhost:5173/node_modules/.vite/deps/react.js?v=e46e0ed1", "resourceType": "script"}, {"timestamp": 1754250058503, "type": "request", "method": "GET", "url": "http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=e46e0ed1", "resourceType": "script"}, {"timestamp": 1754250058504, "type": "request", "method": "GET", "url": "http://localhost:5173/App.tsx", "resourceType": "script"}, {"timestamp": 1754250058504, "type": "response", "status": 200, "url": "http://localhost:5173/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=e46e0ed1", "contentType": "text/javascript", "timing": {"startTime": 1754250058503.127, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.333, "responseStart": 0.975, "responseEnd": 1.353}}, {"timestamp": 1754250058505, "type": "request", "method": "GET", "url": "http://localhost:5173/node_modules/.vite/deps/chunk-VHW23IB5.js?v=e46e0ed1", "resourceType": "script"}, {"timestamp": 1754250058505, "type": "response", "status": 200, "url": "http://localhost:5173/node_modules/.vite/deps/react.js?v=e46e0ed1", "contentType": "text/javascript", "timing": {"startTime": 1754250058503.2207, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.262, "responseStart": 1.151, "responseEnd": 1.677}}, {"timestamp": 1754250058509, "type": "response", "status": 200, "url": "http://localhost:5173/App.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058503.3071, "domainLookupStart": 0.024, "domainLookupEnd": 0.027, "connectStart": 0.027, "secureConnectionStart": -1, "connectEnd": 0.165, "requestStart": 0.207, "responseStart": 4.178, "responseEnd": 5.127}}, {"timestamp": 1754250058509, "type": "response", "status": 200, "url": "http://localhost:5173/node_modules/.vite/deps/chunk-VHW23IB5.js?v=e46e0ed1", "contentType": "text/javascript", "timing": {"startTime": 1754250058504.8179, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.044, "responseStart": 2.859, "responseEnd": 3.653}}, {"timestamp": 1754250058509, "type": "request", "method": "GET", "url": "http://localhost:5173/contexts/AuthContext.tsx", "resourceType": "script"}, {"timestamp": 1754250058509, "type": "request", "method": "GET", "url": "http://localhost:5173/contexts/SettingsContext.tsx", "resourceType": "script"}, {"timestamp": 1754250058509, "type": "request", "method": "GET", "url": "http://localhost:5173/contexts/TaskContext.tsx", "resourceType": "script"}, {"timestamp": 1754250058510, "type": "request", "method": "GET", "url": "http://localhost:5173/components/auth/LoginPage.tsx", "resourceType": "script"}, {"timestamp": 1754250058510, "type": "request", "method": "GET", "url": "http://localhost:5173/components/layout/MainLayout.tsx", "resourceType": "script"}, {"timestamp": 1754250058511, "type": "response", "status": 200, "url": "http://localhost:5173/contexts/SettingsContext.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058509.099, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.54, "responseStart": 0.948, "responseEnd": 2.386}}, {"timestamp": 1754250058512, "type": "response", "status": 200, "url": "http://localhost:5173/contexts/TaskContext.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058509.27, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.468, "responseStart": 0.885, "responseEnd": 2.402}}, {"timestamp": 1754250058513, "type": "response", "status": 200, "url": "http://localhost:5173/contexts/AuthContext.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058509.01, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.604, "responseStart": 0.863, "responseEnd": 2.372}}, {"timestamp": 1754250058514, "type": "response", "status": 200, "url": "http://localhost:5173/components/layout/MainLayout.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058509.5098, "domainLookupStart": 0.032, "domainLookupEnd": 0.034, "connectStart": 0.034, "secureConnectionStart": -1, "connectEnd": 0.078, "requestStart": 0.773, "responseStart": 1.037, "responseEnd": 2.39}}, {"timestamp": 1754250058514, "type": "response", "status": 200, "url": "http://localhost:5173/components/auth/LoginPage.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058509.353, "domainLookupStart": 0.022, "domainLookupEnd": 0.024, "connectStart": 0.024, "secureConnectionStart": -1, "connectEnd": 0.237, "requestStart": 0.665, "responseStart": 1.059, "responseEnd": 2.384}}, {"timestamp": 1754250058514, "type": "request", "method": "GET", "url": "http://localhost:5173/services/apiService.ts", "resourceType": "script"}, {"timestamp": 1754250058514, "type": "request", "method": "GET", "url": "http://localhost:5173/types.ts", "resourceType": "script"}, {"timestamp": 1754250058514, "type": "request", "method": "GET", "url": "http://localhost:5173/utils/dateUtils.ts", "resourceType": "script"}, {"timestamp": 1754250058514, "type": "request", "method": "GET", "url": "http://localhost:5173/components/layout/Sidebar.tsx", "resourceType": "script"}, {"timestamp": 1754250058514, "type": "request", "method": "GET", "url": "http://localhost:5173/views/TasksView.tsx", "resourceType": "script"}, {"timestamp": 1754250058514, "type": "request", "method": "GET", "url": "http://localhost:5173/views/SettingsView.tsx", "resourceType": "script"}, {"timestamp": 1754250058514, "type": "request", "method": "GET", "url": "http://localhost:5173/views/ProfileView.tsx", "resourceType": "script"}, {"timestamp": 1754250058514, "type": "request", "method": "GET", "url": "http://localhost:5173/components/layout/Header.tsx", "resourceType": "script"}, {"timestamp": 1754250058514, "type": "request", "method": "GET", "url": "http://localhost:5173/components/icons/Icons.tsx", "resourceType": "script"}, {"timestamp": 1754250058514, "type": "response", "status": 200, "url": "http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=e46e0ed1", "contentType": "text/javascript", "timing": {"startTime": 1754250058503.266, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.231, "responseStart": 3.863, "responseEnd": 7.321}}, {"timestamp": 1754250058514, "type": "response", "status": 200, "url": "http://localhost:5173/types.ts", "contentType": "text/javascript", "timing": {"startTime": 1754250058512.295, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.273, "responseStart": 0.528, "responseEnd": 0.887}}, {"timestamp": 1754250058514, "type": "response", "status": 200, "url": "http://localhost:5173/utils/dateUtils.ts", "contentType": "text/javascript", "timing": {"startTime": 1754250058512.336, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.58, "responseStart": 0.716, "responseEnd": 1.276}}, {"timestamp": 1754250058515, "type": "response", "status": 200, "url": "http://localhost:5173/components/layout/Sidebar.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058512.424, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.518, "responseStart": 0.754, "responseEnd": 1.247}}, {"timestamp": 1754250058515, "type": "response", "status": 200, "url": "http://localhost:5173/views/TasksView.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058512.46, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.496, "responseStart": 0.794, "responseEnd": 1.327}}, {"timestamp": 1754250058515, "type": "response", "status": 200, "url": "http://localhost:5173/services/apiService.ts", "contentType": "text/javascript", "timing": {"startTime": 1754250058512.218, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.152, "responseStart": 0.429, "responseEnd": 1.1}}, {"timestamp": 1754250058515, "type": "response", "status": 200, "url": "http://localhost:5173/views/SettingsView.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058512.495, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.476, "responseStart": 0.828, "responseEnd": 1.402}}, {"timestamp": 1754250058515, "type": "response", "status": 200, "url": "http://localhost:5173/views/ProfileView.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058512.5452, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.88, "responseStart": 0.997, "responseEnd": 1.457}}, {"timestamp": 1754250058515, "type": "response", "status": 200, "url": "http://localhost:5173/components/layout/Header.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058512.606, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.878, "responseStart": 1.051, "responseEnd": 1.446}}, {"timestamp": 1754250058515, "type": "response", "status": 200, "url": "http://localhost:5173/components/icons/Icons.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058512.792, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 1.023, "responseStart": 1.204, "responseEnd": 1.599}}, {"timestamp": 1754250058516, "type": "request", "method": "GET", "url": "http://localhost:5173/components/tasks/WeatherWidget.tsx", "resourceType": "script"}, {"timestamp": 1754250058516, "type": "request", "method": "GET", "url": "http://localhost:5173/components/tasks/TaskList.tsx", "resourceType": "script"}, {"timestamp": 1754250058516, "type": "request", "method": "GET", "url": "http://localhost:5173/components/tasks/CalendarWidget.tsx", "resourceType": "script"}, {"timestamp": 1754250058516, "type": "request", "method": "GET", "url": "http://localhost:5173/components/tasks/TasksHeader.tsx", "resourceType": "script"}, {"timestamp": 1754250058516, "type": "request", "method": "GET", "url": "http://localhost:5173/components/tasks/TaskFormModal.tsx", "resourceType": "script"}, {"timestamp": 1754250058516, "type": "response", "status": 200, "url": "http://localhost:5173/components/tasks/WeatherWidget.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058515.436, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.176, "responseStart": 0.333, "responseEnd": 0.699}}, {"timestamp": 1754250058517, "type": "response", "status": 200, "url": "http://localhost:5173/components/tasks/TaskList.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058515.508, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.166, "responseStart": 0.38, "responseEnd": 0.688}}, {"timestamp": 1754250058517, "type": "request", "method": "GET", "url": "http://localhost:5173/services/weatherService.ts", "resourceType": "script"}, {"timestamp": 1754250058517, "type": "request", "method": "GET", "url": "http://localhost:5173/components/tasks/TaskCard.tsx", "resourceType": "script"}, {"timestamp": 1754250058517, "type": "response", "status": 200, "url": "http://localhost:5173/components/tasks/CalendarWidget.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058515.549, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.142, "responseStart": 0.543, "responseEnd": 0.97}}, {"timestamp": 1754250058517, "type": "response", "status": 200, "url": "http://localhost:5173/components/tasks/TasksHeader.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058515.582, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.121, "responseStart": 0.602, "responseEnd": 0.996}}, {"timestamp": 1754250058517, "type": "response", "status": 200, "url": "http://localhost:5173/components/tasks/TaskFormModal.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058515.6519, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.067, "responseStart": 0.631, "responseEnd": 1.011}}, {"timestamp": 1754250058517, "type": "request", "method": "GET", "url": "http://localhost:5173/components/ui/SearchInput.tsx", "resourceType": "script"}, {"timestamp": 1754250058517, "type": "request", "method": "GET", "url": "http://localhost:5173/components/tasks/DateTimeDisplay.tsx", "resourceType": "script"}, {"timestamp": 1754250058517, "type": "response", "status": 200, "url": "http://localhost:5173/services/weatherService.ts", "contentType": "text/javascript", "timing": {"startTime": 1754250058516.495, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.109, "responseStart": 0.226, "responseEnd": 0.543}}, {"timestamp": 1754250058517, "type": "response", "status": 200, "url": "http://localhost:5173/components/tasks/TaskCard.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058516.5469, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.077, "responseStart": 0.26, "responseEnd": 0.531}}, {"timestamp": 1754250058517, "type": "response", "status": 200, "url": "http://localhost:5173/components/tasks/DateTimeDisplay.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058517.0469, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.05, "responseStart": 0.202, "responseEnd": 0.41}}, {"timestamp": 1754250058518, "type": "response", "status": 200, "url": "http://localhost:5173/components/ui/SearchInput.tsx", "contentType": "text/javascript", "timing": {"startTime": 1754250058516.9749, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.102, "responseStart": 0.182, "responseEnd": 0.481}}, {"timestamp": 1754250058547, "type": "request", "method": "GET", "url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2", "resourceType": "font"}, {"timestamp": 1754250058549, "type": "request", "method": "POST", "url": "http://localhost:3001/api/tasks/seed", "resourceType": "fetch"}, {"timestamp": 1754250058549, "type": "request", "method": "GET", "url": "http://localhost:3001/api/settings", "resourceType": "fetch"}, {"timestamp": 1754250058549, "type": "request", "method": "GET", "url": "http://localhost:3001/api/auth/session", "resourceType": "fetch"}, {"timestamp": 1754250058549, "type": "request", "method": "POST", "url": "http://localhost:3001/api/tasks/seed", "resourceType": "fetch"}, {"timestamp": 1754250058549, "type": "request", "method": "GET", "url": "http://localhost:3001/api/settings", "resourceType": "fetch"}, {"timestamp": 1754250058549, "type": "request", "method": "GET", "url": "http://localhost:3001/api/auth/session", "resourceType": "fetch"}, {"timestamp": 1754250058549, "type": "request", "method": "GET", "url": "http://localhost:5173/vite.svg", "resourceType": "other"}, {"timestamp": 1754250058551, "type": "response", "status": 429, "url": "http://localhost:3001/api/settings", "contentType": "application/json; charset=utf-8", "timing": {"startTime": 1754250058549.4358, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.162, "responseStart": 1.116, "responseEnd": 2.809}}, {"timestamp": 1754250058551, "type": "response", "status": 200, "url": "http://localhost:5173/vite.svg", "contentType": "text/html", "timing": {"startTime": 1754250058548.99, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.377, "responseStart": 1.946, "responseEnd": 2.446}}, {"timestamp": 1754250058551, "type": "response", "status": 429, "url": "http://localhost:3001/api/tasks/seed", "contentType": "application/json; charset=utf-8", "timing": {"startTime": 1754250058548.951, "domainLookupStart": 0, "domainLookupEnd": 0, "connectStart": 0, "secureConnectionStart": -1, "connectEnd": 0.244, "requestStart": 0.717, "responseStart": 2.131, "responseEnd": 3.455}}, {"timestamp": 1754250058552, "type": "response", "status": 429, "url": "http://localhost:3001/api/auth/session", "contentType": "application/json; charset=utf-8", "timing": {"startTime": 1754250058549.509, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.179, "responseStart": 1.732, "responseEnd": 3.439}}, {"timestamp": 1754250058552, "type": "response", "status": 429, "url": "http://localhost:3001/api/tasks/seed", "contentType": "application/json; charset=utf-8", "timing": {"startTime": 1754250058549.581, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.137, "responseStart": 1.786, "responseEnd": 3.224}}, {"timestamp": 1754250058552, "type": "response", "status": 429, "url": "http://localhost:3001/api/settings", "contentType": "application/json; charset=utf-8", "timing": {"startTime": 1754250058549.903, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.948, "responseStart": 1.604, "responseEnd": 3.181}}, {"timestamp": 1754250058552, "type": "response", "status": 429, "url": "http://localhost:3001/api/auth/session", "contentType": "application/json; charset=utf-8", "timing": {"startTime": 1754250058550.903, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.732, "responseStart": 0.864, "responseEnd": 3.321}}, {"timestamp": 1754250058618, "type": "response", "status": 200, "url": "https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7.woff2", "contentType": "font/woff2", "timing": {"startTime": 1754250058547.0122, "domainLookupStart": 0.039, "domainLookupEnd": 0.313, "connectStart": 0.394, "secureConnectionStart": 0.394, "connectEnd": 28.993, "requestStart": 29.065, "responseStart": 48.289, "responseEnd": 67.438}}, {"timestamp": 1754250061790, "type": "request", "method": "POST", "url": "http://localhost:3001/api/auth/login", "resourceType": "fetch"}, {"timestamp": 1754250061791, "type": "response", "status": 429, "url": "http://localhost:3001/api/auth/login", "contentType": "application/json; charset=utf-8", "timing": {"startTime": 1754250061779.4548, "domainLookupStart": -1, "domainLookupEnd": -1, "connectStart": -1, "secureConnectionStart": -1, "connectEnd": -1, "requestStart": 0.228, "responseStart": 1.493, "responseEnd": 5.146}}], "consoleLog": [{"timestamp": 1754250058471, "type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation"}, {"timestamp": 1754250058504, "type": "debug", "text": "[vite] connecting..."}, {"timestamp": 1754250058508, "type": "debug", "text": "[vite] connected."}, {"timestamp": 1754250058533, "type": "info", "text": "%cDownload the React DevTools for a better development experience: https://react.dev/link/react-devtools font-weight:bold"}, {"timestamp": 1754250058551, "type": "error", "text": "Failed to load resource: the server responded with a status of 429 (Too Many Requests)"}, {"timestamp": 1754250058551, "type": "error", "text": "Failed to load settings: Error: Too many requests. Please try again later.\n    at ApiService.apiCall (http://localhost:5173/services/apiService.ts:101:15)\n    at async ApiService.getSettings (http://localhost:5173/services/apiService.ts:185:22)\n    at async http://localhost:5173/contexts/SettingsContext.tsx:9:27"}, {"timestamp": 1754250058551, "type": "error", "text": "Failed to load resource: the server responded with a status of 429 (Too Many Requests)"}, {"timestamp": 1754250058552, "type": "error", "text": "Failed to load tasks: Error: Too many requests. Please try again later.\n    at ApiService.apiCall (http://localhost:5173/services/apiService.ts:101:15)\n    at async ApiService.seedInitialTasks (http://localhost:5173/services/apiService.ts:173:7)\n    at async http://localhost:5173/contexts/TaskContext.tsx:41:7"}, {"timestamp": 1754250058552, "type": "error", "text": "Failed to load resource: the server responded with a status of 429 (Too Many Requests)"}, {"timestamp": 1754250058552, "type": "error", "text": "Failed to load resource: the server responded with a status of 429 (Too Many Requests)"}, {"timestamp": 1754250058552, "type": "error", "text": "Failed to load resource: the server responded with a status of 429 (Too Many Requests)"}, {"timestamp": 1754250058552, "type": "error", "text": "Failed to load resource: the server responded with a status of 429 (Too Many Requests)"}, {"timestamp": 1754250058552, "type": "error", "text": "Failed to load tasks: Error: Too many requests. Please try again later.\n    at ApiService.apiCall (http://localhost:5173/services/apiService.ts:101:15)\n    at async ApiService.seedInitialTasks (http://localhost:5173/services/apiService.ts:173:7)\n    at async http://localhost:5173/contexts/TaskContext.tsx:41:7"}, {"timestamp": 1754250058563, "type": "error", "text": "Failed to load settings: Error: Too many requests. Please try again later.\n    at ApiService.apiCall (http://localhost:5173/services/apiService.ts:101:15)\n    at async ApiService.getSettings (http://localhost:5173/services/apiService.ts:185:22)\n    at async http://localhost:5173/contexts/SettingsContext.tsx:9:27"}, {"timestamp": 1754250061791, "type": "error", "text": "Failed to load resource: the server responded with a status of 429 (Too Many Requests)"}], "browserPerformance": {"navigation": {"domContentLoaded": 0.09999999962747097, "loadComplete": 0, "totalTime": 236}, "resources": [{"name": "http://localhost:5173/@vite/client", "duration": 17.59999999962747, "transferSize": 182835, "type": "script"}, {"name": "https://cdn.tailwindcss.com/", "duration": 123.79999999981374, "transferSize": 0, "type": "script"}, {"name": "http://localhost:5173/index.css", "duration": 20.59999999962747, "transferSize": 3498, "type": "link"}, {"name": "https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap", "duration": 90.3999999994412, "transferSize": 1079, "type": "link"}, {"name": "http://localhost:5173/index.tsx", "duration": 17.5, "transferSize": 2559, "type": "script"}, {"name": "http://localhost:5173/node_modules/vite/dist/client/env.mjs", "duration": 3, "transferSize": 4384, "type": "script"}, {"name": "http://localhost:5173/node_modules/.vite/deps/react_jsx-dev-runtime.js?v=e46e0ed1", "duration": 1.2999999998137355, "transferSize": 12521, "type": "script"}, {"name": "http://localhost:5173/node_modules/.vite/deps/react.js?v=e46e0ed1", "duration": 1.699999999254942, "transferSize": 1024, "type": "script"}, {"name": "http://localhost:5173/node_modules/.vite/deps/react-dom_client.js?v=e46e0ed1", "duration": 7.2999999998137355, "transferSize": 918443, "type": "script"}, {"name": "http://localhost:5173/App.tsx", "duration": 5.099999999627471, "transferSize": 6633, "type": "script"}, {"name": "http://localhost:5173/node_modules/.vite/deps/chunk-VHW23IB5.js?v=e46e0ed1", "duration": 3.599999999627471, "transferSize": 44065, "type": "script"}, {"name": "http://localhost:5173/contexts/AuthContext.tsx", "duration": 2.3999999994412065, "transferSize": 7645, "type": "script"}, {"name": "http://localhost:5173/contexts/SettingsContext.tsx", "duration": 2.400000000372529, "transferSize": 5563, "type": "script"}, {"name": "http://localhost:5173/contexts/TaskContext.tsx", "duration": 2.400000000372529, "transferSize": 14756, "type": "script"}, {"name": "http://localhost:5173/components/layout/MainLayout.tsx", "duration": 2.3999999994412065, "transferSize": 9974, "type": "script"}, {"name": "http://localhost:5173/components/auth/LoginPage.tsx", "duration": 2.3999999994412065, "transferSize": 20266, "type": "script"}, {"name": "http://localhost:5173/types.ts", "duration": 1, "transferSize": 3163, "type": "script"}, {"name": "http://localhost:5173/services/apiService.ts", "duration": 1.099999999627471, "transferSize": 23793, "type": "script"}, {"name": "http://localhost:5173/utils/dateUtils.ts", "duration": 1.300000000745058, "transferSize": 9939, "type": "script"}, {"name": "http://localhost:5173/components/layout/Sidebar.tsx", "duration": 1.2000000001862645, "transferSize": 13457, "type": "script"}, {"name": "http://localhost:5173/views/TasksView.tsx", "duration": 1.3999999994412065, "transferSize": 6788, "type": "script"}, {"name": "http://localhost:5173/views/SettingsView.tsx", "duration": 1.400000000372529, "transferSize": 8473, "type": "script"}, {"name": "http://localhost:5173/views/ProfileView.tsx", "duration": 1.5, "transferSize": 26980, "type": "script"}, {"name": "http://localhost:5173/components/layout/Header.tsx", "duration": 1.3999999994412065, "transferSize": 7066, "type": "script"}, {"name": "http://localhost:5173/components/icons/Icons.tsx", "duration": 1.599999999627471, "transferSize": 32943, "type": "script"}, {"name": "http://localhost:5173/components/tasks/WeatherWidget.tsx", "duration": 0.7999999998137355, "transferSize": 20392, "type": "script"}, {"name": "http://localhost:5173/components/tasks/TaskList.tsx", "duration": 0.7000000001862645, "transferSize": 13866, "type": "script"}, {"name": "http://localhost:5173/components/tasks/CalendarWidget.tsx", "duration": 1, "transferSize": 17631, "type": "script"}, {"name": "http://localhost:5173/components/tasks/TasksHeader.tsx", "duration": 0.8999999994412065, "transferSize": 16663, "type": "script"}, {"name": "http://localhost:5173/components/tasks/TaskFormModal.tsx", "duration": 1, "transferSize": 43441, "type": "script"}, {"name": "http://localhost:5173/services/weatherService.ts", "duration": 0.5, "transferSize": 12819, "type": "script"}, {"name": "http://localhost:5173/components/tasks/TaskCard.tsx", "duration": 0.5, "transferSize": 25875, "type": "script"}, {"name": "http://localhost:5173/components/tasks/DateTimeDisplay.tsx", "duration": 0.5, "transferSize": 3668, "type": "script"}, {"name": "http://localhost:5173/components/ui/SearchInput.tsx", "duration": 0.5, "transferSize": 4438, "type": "script"}, {"name": "http://localhost:3001/api/tasks/seed", "duration": 3.3999999994412065, "transferSize": 0, "type": "fetch"}, {"name": "http://localhost:3001/api/settings", "duration": 2.699999999254942, "transferSize": 0, "type": "fetch"}, {"name": "http://localhost:3001/api/auth/session", "duration": 3.2999999998137355, "transferSize": 0, "type": "fetch"}, {"name": "http://localhost:3001/api/tasks/seed", "duration": 3.2000000001862645, "transferSize": 0, "type": "fetch"}, {"name": "http://localhost:3001/api/settings", "duration": 3.099999999627471, "transferSize": 0, "type": "fetch"}, {"name": "http://localhost:3001/api/auth/session", "duration": 3.2999999998137355, "transferSize": 0, "type": "fetch"}, {"name": "http://localhost:5173/vite.svg", "duration": 2.3999999994412065, "transferSize": 3498, "type": "other"}, {"name": "http://localhost:3001/api/auth/login", "duration": 4.8999999994412065, "transferSize": 0, "type": "fetch"}]}, "summary": {"totalLoadTime": 1324, "domContentLoaded": 0.09999999962747097, "loadComplete": 0, "loadingIndicatorsFound": false}}