# LifeTracker - Personal Task Management

This is a React 19 + TypeScript personal task management application with an Express.js backend.

## Run Locally

**Prerequisites:** Node.js 18+ and npm

### Frontend Setup

1. Install dependencies:
   ```bash
   npm install
   ```

2. Configure environment variables in [.env.local](.env.local):
   ```bash
   VITE_API_BASE_URL=http://localhost:3001/api
   ```

3. Run the frontend development server:
   ```bash
   npm run dev
   ```

### Backend Setup

1. Navigate to the backend directory:
   ```bash
   cd backend
   ```

2. Install backend dependencies:
   ```bash
   npm install
   ```

3. Configure environment variables (copy .env.example to .env and update values):
   ```bash
   cp .env.example .env
   ```

4. Run the backend server:
   ```bash
   npm run dev
   ```

The application will be available at `http://localhost:5173` with the API running on `http://localhost:3001`.
