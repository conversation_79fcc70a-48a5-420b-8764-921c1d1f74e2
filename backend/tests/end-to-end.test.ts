/**
 * End-to-End Workflow Testing
 * Tests complete user workflows from login to task management
 */

import request from 'supertest';
import { Application } from 'express';
import { LifeTrackerApp } from '../src/app';

describe('End-to-End User Workflows', () => {
  let app: Application;
  let lifeTrackerApp: LifeTrackerApp;

  beforeAll(async () => {
    lifeTrackerApp = new LifeTrackerApp();
    app = lifeTrackerApp.app;
    await lifeTrackerApp.initialize();
  });

  describe('Complete User Session Workflow', () => {
    let authToken: string;
    let taskId: string;

    test('1. User should be able to login successfully', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway',
          password: 'Lookup88?'
        })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data.user).toHaveProperty('username', 'scharway');

      authToken = response.body.data.token;
    });

    test('2. User should be able to check session status', async () => {
      const response = await request(app)
        .get('/api/auth/session')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('isValid', true);
    });

    test('3. User should be able to view initial settings', async () => {
      const response = await request(app)
        .get('/api/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('showCompleted');
      expect(response.body.data).toHaveProperty('seeded');
    });

    test('4. User should be able to create a personal task', async () => {
      const newTask = {
        title: 'Complete Project Review',
        description: 'Review and finalize the quarterly project deliverables',
        dueDate: '2025-12-15',
        dueTime: '16:00',
        priority: 'High',
        category: 'Work'
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(newTask)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('title', newTask.title);
      expect(response.body.data).toHaveProperty('isCompleted', false);

      taskId = response.body.data.id;
    });

    test('5. User should be able to view their tasks', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toBeInstanceOf(Array);
      
      const createdTask = response.body.data.find((task: any) => task.id === taskId);
      expect(createdTask).toBeDefined();
      expect(createdTask).toHaveProperty('title', 'Complete Project Review');
    });

    test('6. User should be able to update task details', async () => {
      const updates = {
        description: 'Review and finalize the quarterly project deliverables - Updated with additional requirements',
        priority: 'Medium'
      };

      const response = await request(app)
        .put(`/api/tasks/${taskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updates)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('description', updates.description);
      expect(response.body.data).toHaveProperty('priority', updates.priority);
    });

    test('7. User should be able to mark task as completed', async () => {
      const response = await request(app)
        .put(`/api/tasks/${taskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ isCompleted: true })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('isCompleted', true);
      expect(response.body.data).toHaveProperty('completedAt');
    });

    test('8. User should be able to filter completed tasks', async () => {
      const response = await request(app)
        .get('/api/tasks?completed=true')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toBeInstanceOf(Array);
      
      const completedTask = response.body.data.find((task: any) => task.id === taskId);
      expect(completedTask).toBeDefined();
      expect(completedTask).toHaveProperty('isCompleted', true);
    });

    test('9. User should be able to update settings', async () => {
      const response = await request(app)
        .put('/api/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          showCompleted: false
        })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('showCompleted', false);
    });

    test('10. User should be able to delete tasks', async () => {
      const response = await request(app)
        .delete(`/api/tasks/${taskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('deletedTaskId', taskId);

      // Verify task is deleted
      await request(app)
        .get(`/api/tasks/${taskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('Recurring Task Workflow', () => {
    let authToken: string;
    let recurringTaskId: string;

    beforeAll(async () => {
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway',
          password: 'Lookup88?'
        });
      authToken = loginResponse.body.data.token;
    });

    test('1. User should be able to create a recurring task', async () => {
      const recurringTask = {
        title: 'Weekly Team Meeting',
        description: 'Attend the weekly team standup meeting',
        dueDate: '2025-12-20',
        dueTime: '09:00',
        priority: 'High',
        category: 'Meetings',
        isRecurring: true,
        recurrenceType: 'Weekly',
        recurrenceInterval: 1
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(recurringTask)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('isRecurring', true);
      expect(response.body.data).toHaveProperty('recurrenceType', 'Weekly');

      recurringTaskId = response.body.data.id;
    });

    test('2. User should be able to complete a recurring task instance', async () => {
      const response = await request(app)
        .put(`/api/tasks/${recurringTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ isCompleted: true })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      
      // The task should either be completed or rescheduled for next occurrence
      const task = response.body.data;
      if (task.isCompleted) {
        expect(task).toHaveProperty('completedAt');
      } else {
        expect(task).toHaveProperty('isRecurring', true);
      }
    });

    test('3. User should see task instances in their task list', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      
      const weeklyMeetingTasks = response.body.data.filter((task: any) => 
        task.title === 'Weekly Team Meeting'
      );
      
      expect(weeklyMeetingTasks.length).toBeGreaterThanOrEqual(1);
    });
  });

  describe('System Health and Monitoring Workflow', () => {
    test('1. System should provide health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('status', 'healthy');
      expect(response.body.data).toHaveProperty('uptime');
      expect(response.body.data).toHaveProperty('database');
      expect(response.body.data).toHaveProperty('memory');
    });

    test('2. System should provide detailed database health', async () => {
      const response = await request(app)
        .get('/api/health/database')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('connected', true);
      expect(response.body.data).toHaveProperty('taskCount');
      expect(response.body.data).toHaveProperty('settingsCount');
    });
  });

  describe('Error Recovery Workflow', () => {
    let authToken: string;

    beforeAll(async () => {
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway',
          password: 'Lookup88?'
        });
      authToken = loginResponse.body.data.token;
    });

    test('1. System should handle invalid task operations gracefully', async () => {
      // Try to get non-existent task
      const response1 = await request(app)
        .get('/api/tasks/nonexistent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response1.body).toHaveProperty('success', false);

      // Try to update non-existent task
      const response2 = await request(app)
        .put('/api/tasks/nonexistent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ title: 'Updated' })
        .expect(400);

      expect(response2.body).toHaveProperty('success', false);

      // Try to delete non-existent task
      const response3 = await request(app)
        .delete('/api/tasks/nonexistent-id')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response3.body).toHaveProperty('success', false);
    });

    test('2. System should validate input data consistently', async () => {
      const invalidTask = {
        // Missing required fields
        description: 'Task without title',
        priority: 'InvalidPriority'
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidTask)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    test('3. System should maintain security under error conditions', async () => {
      // Try to access protected endpoint without auth
      const response = await request(app)
        .get('/api/tasks')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error).not.toContain('password');
      expect(response.body.error).not.toContain('database');
    });
  });
});