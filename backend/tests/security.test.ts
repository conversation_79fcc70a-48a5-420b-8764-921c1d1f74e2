/**
 * Security Testing Suite
 * Comprehensive security validation for production deployment
 */

import request from 'supertest';
import { Application } from 'express';
import { LifeTrackerApp } from '../src/app';

describe('Security Validation', () => {
  let app: Application;
  let authToken: string;
  let lifeTrackerApp: LifeTrackerApp;

  beforeAll(async () => {
    lifeTrackerApp = new LifeTrackerApp();
    app = lifeTrackerApp.app;
    await lifeTrackerApp.initialize();

    // Get auth token for testing
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'scharway',
        password: 'Lookup88?'
      });
    authToken = loginResponse.body.data.token;
  });

  describe('Authentication Security', () => {
    test('should reject requests without authentication', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error).toHaveProperty('code');
    });

    test('should reject requests with invalid token', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set('Authorization', 'Bearer invalid-token-here')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('should reject requests with malformed authorization header', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set('Authorization', 'InvalidFormat token-here')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('should enforce JWT token expiration', async () => {
      // This would require a way to create expired tokens for testing
      // For now, we'll test that tokens have reasonable expiration times
      const response = await request(app)
        .get('/api/auth/session')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      const expiresAt = new Date(response.body.data.expiresAt);
      const now = new Date();
      const timeDiff = expiresAt.getTime() - now.getTime();
      
      // Token should expire within 5 minutes (300000ms)
      expect(timeDiff).toBeLessThanOrEqual(300000);
      expect(timeDiff).toBeGreaterThan(0);
    });
  });

  describe('Input Validation Security', () => {
    test('should prevent SQL injection in task creation', async () => {
      const maliciousTask = {
        title: "'; DROP TABLE tasks; --",
        description: "SQL injection attempt",
        dueDate: '2025-12-31',
        dueTime: '14:30',
        priority: 'High'
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(maliciousTask);

      // Should either succeed (input sanitized) or fail validation
      if (response.status === 201) {
        expect(response.body.data.title).toBe(maliciousTask.title);
      } else {
        expect(response.status).toBe(400);
      }

      // Verify database integrity by checking we can still query tasks
      const tasksResponse = await request(app)
        .get('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(tasksResponse.body).toHaveProperty('success', true);
    });

    test('should validate task input lengths', async () => {
      const longTask = {
        title: 'A'.repeat(300), // Exceeds likely max length
        description: 'B'.repeat(2000), // Exceeds likely max length
        dueDate: '2025-12-31',
        dueTime: '14:30',
        priority: 'High'
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(longTask)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error).toHaveProperty('code');
    });

    test('should validate enum values', async () => {
      const invalidTask = {
        title: 'Test Task',
        dueDate: '2025-12-31',
        dueTime: '14:30',
        priority: 'SuperCritical' // Invalid priority
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidTask)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });

    test('should validate date formats', async () => {
      const invalidTask = {
        title: 'Test Task',
        dueDate: 'not-a-date',
        dueTime: 'not-a-time',
        priority: 'High'
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidTask)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Rate Limiting Security', () => {
    test('should apply rate limiting to authentication endpoints', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway',
          password: 'wrongpassword'
        });

      // Should have rate limiting headers
      expect(response.headers).toHaveProperty('ratelimit-limit');
      expect(response.headers).toHaveProperty('ratelimit-remaining');
      expect(response.headers).toHaveProperty('ratelimit-reset');
    });

    test('should apply rate limiting to general API endpoints', async () => {
      const response = await request(app)
        .get('/api/health');

      // General endpoints should have higher limits
      const limit = parseInt(response.headers['ratelimit-limit'] || '0');
      expect(limit).toBeGreaterThanOrEqual(1000);
    });
  });

  describe('CORS Security', () => {
    test('should handle CORS for allowed origins', async () => {
      const response = await request(app)
        .options('/api/tasks')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .set('Access-Control-Request-Headers', 'Content-Type,Authorization');

      expect(response.headers).toHaveProperty('access-control-allow-origin');
      expect(response.headers).toHaveProperty('access-control-allow-methods');
      expect(response.headers).toHaveProperty('access-control-allow-headers');
    });

    test('should include credentials flag in CORS', async () => {
      const response = await request(app)
        .get('/api/health')
        .set('Origin', 'http://localhost:3000');

      expect(response.headers).toHaveProperty('access-control-allow-credentials', 'true');
    });
  });

  describe('Security Headers', () => {
    test('should include comprehensive security headers', async () => {
      const response = await request(app)
        .get('/api/health');

      // Helmet.js security headers
      expect(response.headers).toHaveProperty('x-frame-options', 'DENY');
      expect(response.headers).toHaveProperty('x-content-type-options', 'nosniff');
      expect(response.headers).toHaveProperty('x-dns-prefetch-control', 'off');
      expect(response.headers).toHaveProperty('x-download-options', 'noopen');
      expect(response.headers).toHaveProperty('x-permitted-cross-domain-policies', 'none');
      expect(response.headers).toHaveProperty('referrer-policy', 'strict-origin-when-cross-origin');
      expect(response.headers).toHaveProperty('strict-transport-security');
      expect(response.headers).toHaveProperty('content-security-policy');
    });

    test('should prevent caching of sensitive data', async () => {
      const response = await request(app)
        .get('/api/auth/session')
        .set('Authorization', `Bearer ${authToken}`);

      // Should have no-cache headers for auth endpoints
      expect(response.headers).toHaveProperty('cache-control');
      expect(response.headers['cache-control']).toMatch(/no-store|no-cache/);
    });
  });

  describe('Error Information Disclosure', () => {
    test('should not leak sensitive information in error messages', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway',
          password: 'wrongpassword'
        })
        .expect(401);

      // Error message should be generic, not revealing internal details
      expect(response.body.error.message).not.toMatch(/database|sql|internal|stack/i);
    });

    test('should not expose system internals in 500 errors', async () => {
      // This would require triggering a server error
      // For now, verify error handling structure is secure
      const response = await request(app)
        .get('/api/nonexistent-endpoint')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error).not.toHaveProperty('stack');
      expect(response.body.error).not.toHaveProperty('details');
    });
  });

  describe('Request Size and Content Security', () => {
    test('should reject oversized request bodies', async () => {
      const oversizedTask = {
        title: 'Test Task',
        description: 'A'.repeat(50000), // Very large description
        dueDate: '2025-12-31',
        dueTime: '14:30',
        priority: 'High'
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(oversizedTask);

      // Should either be rejected or truncated
      if (response.status === 413) {
        expect(response.status).toBe(413); // Payload too large
      } else if (response.status === 400) {
        expect(response.body).toHaveProperty('success', false);
      }
    });

    test('should validate content-type headers', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'text/plain')
        .send('plain text data')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Session Management Security', () => {
    test('should generate unique request IDs', async () => {
      const response1 = await request(app).get('/api/health');
      const response2 = await request(app).get('/api/health');

      const requestId1 = response1.body.metadata.requestId;
      const requestId2 = response2.body.metadata.requestId;

      expect(requestId1).toBeDefined();
      expect(requestId2).toBeDefined();
      expect(requestId1).not.toBe(requestId2);
    });

    test('should include request ID in responses', async () => {
      const response = await request(app)
        .get('/api/health');

      expect(response.body.metadata).toHaveProperty('requestId');
      expect(response.headers).toHaveProperty('x-request-id');
      expect(response.body.metadata.requestId).toBe(response.headers['x-request-id']);
    });
  });

  describe('Password Security', () => {
    test('should not return password hashes in API responses', async () => {
      const response = await request(app)
        .get('/api/auth/session')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data.user).not.toHaveProperty('password');
      expect(response.body.data.user).not.toHaveProperty('password_hash');
      expect(response.body.data.user).not.toHaveProperty('passwordHash');
    });

    test('should require strong authentication for login', async () => {
      // Test that login requires both username and password
      const responses = await Promise.all([
        request(app).post('/api/auth/login').send({ username: 'scharway' }),
        request(app).post('/api/auth/login').send({ password: 'Lookup88?' }),
        request(app).post('/api/auth/login').send({})
      ]);

      responses.forEach(response => {
        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('success', false);
      });
    });
  });
});