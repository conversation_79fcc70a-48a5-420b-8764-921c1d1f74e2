/**
 * Database Operations Test Suite
 * Tests database schema, CRUD operations, transactions, and maintenance
 */

import request from 'supertest';
import { Application } from 'express';
import { LifeTrackerApp } from '../src/app';

describe('Database Operations', () => {
  let app: Application;
  let authToken: string;
  let lifeTrackerApp: LifeTrackerApp;

  beforeAll(async () => {
    lifeTrackerApp = new LifeTrackerApp();
    app = lifeTrackerApp.app;
    await lifeTrackerApp.initialize();

    // Get auth token for testing
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        username: 'scharway',
        password: 'Lookup88?'
      });
    authToken = loginResponse.body.data.token;
  });

  describe('Database Schema and Initialization', () => {
    test('should have initialized database with correct schema', async () => {
      const response = await request(app)
        .get('/api/health/database')
        .expect(200);

      expect(response.body.data).toHaveProperty('connected', true);
      expect(response.body.data).toHaveProperty('taskCount');
      expect(response.body.data).toHaveProperty('settingsCount');
      expect(response.body.data.settingsCount).toBeGreaterThanOrEqual(2);
    });

    test('should have created default settings', async () => {
      const response = await request(app)
        .get('/api/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveProperty('showCompleted');
      expect(response.body.data).toHaveProperty('seeded');
      expect(typeof response.body.data.showCompleted).toBe('boolean');
      expect(typeof response.body.data.seeded).toBe('boolean');
    });
  });

  describe('Task CRUD Operations', () => {
    let createdTaskId: string;

    test('should create task with all fields', async () => {
      const taskData = {
        title: 'Database Test Task',
        description: 'Testing database operations',
        dueDate: '2025-12-31',
        dueTime: '15:30',
        priority: 'High',
        category: 'Testing',
        isRecurring: false
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(taskData)
        .expect(201);

      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('title', taskData.title);
      expect(response.body.data).toHaveProperty('description', taskData.description);
      expect(response.body.data).toHaveProperty('dueDate', taskData.dueDate);
      expect(response.body.data).toHaveProperty('dueTime', taskData.dueTime);
      expect(response.body.data).toHaveProperty('priority', taskData.priority);
      expect(response.body.data).toHaveProperty('category', taskData.category);
      expect(response.body.data).toHaveProperty('isRecurring', taskData.isRecurring);
      expect(response.body.data).toHaveProperty('isCompleted', false);
      expect(response.body.data).toHaveProperty('createdAt');
      expect(response.body.data).toHaveProperty('updatedAt');

      createdTaskId = response.body.data.id;
    });

    test('should read task by ID', async () => {
      const response = await request(app)
        .get(`/api/tasks/${createdTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveProperty('id', createdTaskId);
      expect(response.body.data).toHaveProperty('title', 'Database Test Task');
    });

    test('should update task fields', async () => {
      const updates = {
        title: 'Updated Database Test Task',
        description: 'Updated description',
        priority: 'Medium'
      };

      const response = await request(app)
        .put(`/api/tasks/${createdTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updates)
        .expect(200);

      expect(response.body.data).toHaveProperty('title', updates.title);
      expect(response.body.data).toHaveProperty('description', updates.description);
      expect(response.body.data).toHaveProperty('priority', updates.priority);
      expect(response.body.data).toHaveProperty('updatedAt');

      // Verify updatedAt timestamp changed
      const originalCreatedAt = response.body.data.createdAt;
      const updatedAt = response.body.data.updatedAt;
      expect(new Date(updatedAt)).not.toEqual(new Date(originalCreatedAt));
    });

    test('should mark task as completed', async () => {
      const response = await request(app)
        .put(`/api/tasks/${createdTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ isCompleted: true })
        .expect(200);

      expect(response.body.data).toHaveProperty('isCompleted', true);
      expect(response.body.data).toHaveProperty('completedAt');
      expect(response.body.data.completedAt).not.toBeNull();
    });

    test('should delete task', async () => {
      const response = await request(app)
        .delete(`/api/tasks/${createdTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveProperty('deletedTaskId', createdTaskId);

      // Verify task is deleted
      await request(app)
        .get(`/api/tasks/${createdTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });
  });

  describe('Recurring Task Logic', () => {
    let recurringTaskId: string;

    test('should create recurring task', async () => {
      const recurringTask = {
        title: 'Weekly Report',
        description: 'Submit weekly report',
        dueDate: '2025-12-31',
        dueTime: '17:00',
        priority: 'High',
        isRecurring: true,
        recurrenceType: 'Weekly',
        recurrenceInterval: 1
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(recurringTask)
        .expect(201);

      expect(response.body.data).toHaveProperty('isRecurring', true);
      expect(response.body.data).toHaveProperty('recurrenceType', 'Weekly');
      expect(response.body.data).toHaveProperty('recurrenceInterval', 1);

      recurringTaskId = response.body.data.id;
    });

    test('should handle recurring task completion', async () => {
      // Complete the recurring task
      const completeResponse = await request(app)
        .put(`/api/tasks/${recurringTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ isCompleted: true })
        .expect(200);

      // For recurring tasks, completion should:
      // 1. Create a completed instance (or mark as completed)
      // 2. Update the original task's due date to next occurrence
      const updatedTask = completeResponse.body.data;
      
      // Verify the task handling (exact behavior depends on implementation)
      if (updatedTask.isCompleted) {
        // If this is the completed instance
        expect(updatedTask).toHaveProperty('completedAt');
      } else {
        // If this is the updated recurring task with new due date
        expect(updatedTask).toHaveProperty('isRecurring', true);
        expect(updatedTask).toHaveProperty('dueDate');
      }
    });

    test('should list all task instances including recurring', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toBeInstanceOf(Array);
      
      // Should include tasks related to our recurring task
      const weeklyReportTasks = response.body.data.filter((task: any) => 
        task.title === 'Weekly Report'
      );
      
      expect(weeklyReportTasks.length).toBeGreaterThanOrEqual(1);
    });
  });

  describe('Settings CRUD Operations', () => {
    test('should update individual settings', async () => {
      const response = await request(app)
        .put('/api/settings/showCompleted')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ value: false })
        .expect(200);

      expect(response.body.data).toHaveProperty('key', 'showCompleted');
      expect(response.body.data).toHaveProperty('value', false);
    });

    test('should update multiple settings', async () => {
      const updates = {
        showCompleted: true,
        seeded: true
      };

      const response = await request(app)
        .put('/api/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updates)
        .expect(200);

      expect(response.body.data).toHaveProperty('showCompleted', true);
      expect(response.body.data).toHaveProperty('seeded', true);
    });

    test('should persist settings across requests', async () => {
      // Set a specific value
      await request(app)
        .put('/api/settings/showCompleted')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ value: false })
        .expect(200);

      // Verify it persists
      const response = await request(app)
        .get('/api/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toHaveProperty('showCompleted', false);
    });
  });

  describe('Data Integrity and Constraints', () => {
    test('should enforce unique constraints where applicable', async () => {
      // This test depends on schema constraints
      // For single-user system, we might not have user-level uniqueness
      // But we could test other constraints
      const response = await request(app)
        .get('/api/health/database')
        .expect(200);

      expect(response.body.data).toHaveProperty('connected', true);
    });

    test('should handle concurrent operations', async () => {
      const taskData = {
        title: 'Concurrent Test Task',
        dueDate: '2025-12-31',
        dueTime: '12:00',
        priority: 'Medium'
      };

      // Create multiple tasks concurrently
      const promises = Array.from({ length: 5 }, () =>
        request(app)
          .post('/api/tasks')
          .set('Authorization', `Bearer ${authToken}`)
          .send({ ...taskData, title: `${taskData.title} ${Math.random()}` })
      );

      const responses = await Promise.all(promises);
      
      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(201);
        expect(response.body).toHaveProperty('success', true);
      });

      // All should have unique IDs
      const ids = responses.map(r => r.body.data.id);
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(ids.length);
    });

    test('should maintain referential integrity', async () => {
      // Test that related data remains consistent
      const before = await request(app)
        .get('/api/health/database')
        .expect(200);

      const initialTaskCount = before.body.data.taskCount;

      // Create a task
      const createResponse = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Integrity Test Task',
          dueDate: '2025-12-31',
          dueTime: '10:00',
          priority: 'Low'
        })
        .expect(201);

      const taskId = createResponse.body.data.id;

      // Check count increased
      const after = await request(app)
        .get('/api/health/database')
        .expect(200);

      expect(after.body.data.taskCount).toBe(initialTaskCount + 1);

      // Delete the task
      await request(app)
        .delete(`/api/tasks/${taskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      // Check count decreased
      const final = await request(app)
        .get('/api/health/database')
        .expect(200);

      expect(final.body.data.taskCount).toBe(initialTaskCount);
    });
  });

  describe('Database Performance', () => {
    test('should handle bulk operations efficiently', async () => {
      const startTime = Date.now();

      // Create multiple tasks
      const tasks = Array.from({ length: 20 }, (_, i) => ({
        title: `Bulk Task ${i + 1}`,
        dueDate: '2025-12-31',
        dueTime: '09:00',
        priority: 'Low'
      }));

      const promises = tasks.map(task =>
        request(app)
          .post('/api/tasks')
          .set('Authorization', `Bearer ${authToken}`)
          .send(task)
      );

      const responses = await Promise.all(promises);
      const endTime = Date.now();

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(201);
      });

      // Should complete in reasonable time (under 2 seconds for 20 tasks)
      const duration = endTime - startTime;
      expect(duration).toBeLessThan(2000);

      // Clean up
      const deletePromises = responses.map(response =>
        request(app)
          .delete(`/api/tasks/${response.body.data.id}`)
          .set('Authorization', `Bearer ${authToken}`)
      );

      await Promise.all(deletePromises);
    });

    test('should handle large result sets', async () => {
      // This test assumes we have some tasks in the database
      const response = await request(app)
        .get('/api/tasks?limit=100')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.metadata).toHaveProperty('timestamp');
      
      // Response should be returned quickly
      const responseTime = Date.now() - new Date(response.body.metadata.timestamp).getTime();
      expect(responseTime).toBeLessThan(1000);
    });
  });

  describe('Database Maintenance', () => {
    test('should report database size and statistics', async () => {
      const response = await request(app)
        .get('/api/health/database')
        .expect(200);

      expect(response.body.data).toHaveProperty('size');
      expect(response.body.data).toHaveProperty('taskCount');
      expect(response.body.data).toHaveProperty('settingsCount');
      expect(typeof response.body.data.size).toBe('number');
      expect(typeof response.body.data.taskCount).toBe('number');
      expect(typeof response.body.data.settingsCount).toBe('number');
    });

    test('should handle database health checks', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body.data.database).toHaveProperty('connected', true);
      expect(response.body.data.database).toHaveProperty('responseTime');
      expect(typeof response.body.data.database.responseTime).toBe('number');
      expect(response.body.data.database.responseTime).toBeLessThan(100);
    });
  });
});