/**
 * Comprehensive API Endpoints Test Suite
 * Tests all endpoints defined in api-specification.md with full coverage
 */

import request from 'supertest';
import { Application } from 'express';
import { LifeTrackerApp } from '../src/app';

describe('LifeTracker API Endpoints', () => {
  let app: Application;
  let authToken: string;
  let lifeTrackerApp: LifeTrackerApp;

  beforeAll(async () => {
    // Initialize the application
    lifeTrackerApp = new LifeTrackerApp();
    app = lifeTrackerApp.app;
    
    // Initialize services 
    try {
      await lifeTrackerApp.initialize();
    } catch (error) {
      console.error('Failed to initialize app for testing:', error);
      throw error;
    }
  });

  afterAll(async () => {
    // Cleanup is handled by the main server process
  });

  describe('Health Endpoints', () => {
    test('GET /api/health should return system health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('status');
      expect(response.body.data).toHaveProperty('timestamp');
      expect(response.body.data).toHaveProperty('uptime');
      expect(response.body.data).toHaveProperty('database');
      expect(response.body.data).toHaveProperty('memory');
      expect(response.body.metadata).toHaveProperty('timestamp');
      expect(response.body.metadata).toHaveProperty('requestId');
    });

    test('GET /api/health/database should return database health', async () => {
      const response = await request(app)
        .get('/api/health/database')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('connected');
      expect(response.body.data).toHaveProperty('size');
      expect(response.body.data).toHaveProperty('taskCount');
      expect(response.body.data).toHaveProperty('settingsCount');
    });
  });

  describe('Authentication Endpoints', () => {
    test('POST /api/auth/login with valid credentials should succeed', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway',
          password: 'Lookup88?'
        })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('expiresAt');
      expect(response.body.data.user).toHaveProperty('username', 'scharway');

      // Store token for subsequent tests
      authToken = response.body.data.token;
    });

    test('POST /api/auth/login with invalid credentials should fail', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway',
          password: 'wrongpassword'
        })
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code');
      expect(response.body.error).toHaveProperty('message');
    });

    test('POST /api/auth/login with missing fields should fail', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway'
          // Missing password
        })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    test('GET /api/auth/session with valid token should return user info', async () => {
      const response = await request(app)
        .get('/api/auth/session')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('user');
      expect(response.body.data).toHaveProperty('expiresAt');
      expect(response.body.data).toHaveProperty('isValid', true);
    });

    test('GET /api/auth/session without token should fail', async () => {
      const response = await request(app)
        .get('/api/auth/session')
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('POST /api/auth/refresh with valid token should return new token', async () => {
      const response = await request(app)
        .post('/api/auth/refresh') 
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('token');
      expect(response.body.data).toHaveProperty('expiresAt');
    });

    test('POST /api/auth/logout should invalidate token', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('message');

      // Get a new token for remaining tests
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway',
          password: 'Lookup88?'
        });
      authToken = loginResponse.body.data.token;
    });
  });

  describe('Tasks Endpoints', () => {
    let createdTaskId: string;

    test('GET /api/tasks should return empty list initially', async () => {
      const response = await request(app)
        .get('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.metadata).toHaveProperty('pagination');
    });

    test('POST /api/tasks should create a new task', async () => {
      const newTask = {
        title: 'Test Task',
        description: 'This is a test task',
        dueDate: '2025-12-31',
        dueTime: '14:30',
        priority: 'High',
        category: 'Testing'
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(newTask)
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('id');
      expect(response.body.data).toHaveProperty('title', newTask.title);
      expect(response.body.data).toHaveProperty('description', newTask.description);
      expect(response.body.data).toHaveProperty('priority', newTask.priority);
      expect(response.body.data).toHaveProperty('isCompleted', false);

      createdTaskId = response.body.data.id;
    });

    test('POST /api/tasks with invalid data should fail', async () => {
      const invalidTask = {
        // Missing required title
        description: 'This task has no title',
        dueDate: '2025-12-31',
        dueTime: '14:30',
        priority: 'High'
      };

      const response = await request(app)
        .post('/api/tasks')
        .set('Authorization', `Bearer ${authToken}`)
        .send(invalidTask)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });

    test('POST /api/tasks without authentication should fail', async () => {
      const newTask = {
        title: 'Unauthorized Task',
        dueDate: '2025-12-31',
        dueTime: '14:30',
        priority: 'Low'
      };

      const response = await request(app)
        .post('/api/tasks')
        .send(newTask)
        .expect(401);

      expect(response.body).toHaveProperty('success', false);
    });

    test('GET /api/tasks/:id should return specific task', async () => {
      const response = await request(app)
        .get(`/api/tasks/${createdTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('id', createdTaskId);
      expect(response.body.data).toHaveProperty('title', 'Test Task');
    });

    test('GET /api/tasks/:id with invalid ID should fail', async () => {
      const response = await request(app)
        .get('/api/tasks/invalid-uuid')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });

    test('PUT /api/tasks/:id should update task', async () => {
      const updates = {
        title: 'Updated Test Task',
        description: 'This task has been updated',
        isCompleted: true
      };

      const response = await request(app)
        .put(`/api/tasks/${createdTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(updates)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('title', updates.title);
      expect(response.body.data).toHaveProperty('description', updates.description);
      expect(response.body.data).toHaveProperty('isCompleted', true);
      expect(response.body.data).toHaveProperty('completedAt');
    });

    test('DELETE /api/tasks/:id should delete task', async () => {
      const response = await request(app)
        .delete(`/api/tasks/${createdTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('message');
      expect(response.body.data).toHaveProperty('deletedTaskId', createdTaskId);

      // Verify task is deleted
      await request(app)
        .get(`/api/tasks/${createdTaskId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(404);
    });

    test('POST /api/tasks/seed should create initial tasks', async () => {
      const seedTasks = [
        {
          title: 'Seed Task 1',
          description: 'First seed task',
          dueDate: '2025-12-31',
          dueTime: '09:00',
          priority: 'Medium'
        },
        {
          title: 'Seed Task 2',
          description: 'Second seed task',
          dueDate: '2025-12-31',
          dueTime: '15:00',
          priority: 'Low'
        }
      ];

      const response = await request(app)
        .post('/api/tasks/seed')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ tasks: seedTasks })
        .expect(201);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('seededTasks');
      expect(response.body.data).toHaveProperty('count', 2);
      expect(response.body.data.seededTasks).toHaveLength(2);
    });

    test('GET /api/tasks with query parameters should filter results', async () => {
      const response = await request(app)
        .get('/api/tasks?completed=false&limit=10')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.metadata.pagination).toHaveProperty('limit', 10);
    });
  });

  describe('Settings Endpoints', () => {
    test('GET /api/settings should return user settings', async () => {
      const response = await request(app)
        .get('/api/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('showCompleted');
      expect(response.body.data).toHaveProperty('seeded');
    });

    test('PUT /api/settings should update settings', async () => {
      const updates = {
        showCompleted: false,
        seeded: true
      };

      const response = await request(app)
        .put('/api/settings')
        .set('Authorization', `Bearer ${authToken}`)
        .send(updates)
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('showCompleted', false);
      expect(response.body.data).toHaveProperty('seeded', true);
    });

    test('PUT /api/settings/:key should update individual setting', async () => {
      const response = await request(app)
        .put('/api/settings/showCompleted')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ value: true })
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('key', 'showCompleted');
      expect(response.body.data).toHaveProperty('value', true);
    });

    test('PUT /api/settings/:key with invalid key should fail', async () => {
      const response = await request(app)
        .put('/api/settings/invalidKey')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ value: true })
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
    });
  });

  describe('Rate Limiting', () => {
    test('should enforce rate limiting on auth endpoints', async () => {
      // This test would need to be run with a shorter rate limit window for practical testing
      // For now, we'll just verify the rate limit headers are present
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'scharway',
          password: 'wrongpassword'
        });

      expect(response.headers).toHaveProperty('x-ratelimit-limit');
      expect(response.headers).toHaveProperty('x-ratelimit-remaining');
      expect(response.headers).toHaveProperty('x-ratelimit-reset');
    });
  });

  describe('Error Handling', () => {
    test('should return consistent error format for 404', async () => {
      const response = await request(app)
        .get('/api/nonexistent')
        .expect(404);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toHaveProperty('code');
      expect(response.body.error).toHaveProperty('message');
      expect(response.body).toHaveProperty('metadata');
    });

    test('should handle malformed JSON gracefully', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .set('Content-Type', 'application/json')
        .send('{"invalid": json}')
        .expect(400);

      expect(response.body).toHaveProperty('success', false);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('CORS and Security Headers', () => {
    test('should include security headers', async () => {
      const response = await request(app)
        .get('/api/health');

      // Helmet.js security headers
      expect(response.headers).toHaveProperty('x-frame-options');
      expect(response.headers).toHaveProperty('x-content-type-options');
      expect(response.headers).toHaveProperty('x-dns-prefetch-control');
    });

    test('should handle CORS preflight requests', async () => {
      const response = await request(app)
        .options('/api/tasks')
        .set('Origin', 'http://localhost:3000')
        .set('Access-Control-Request-Method', 'POST')
        .set('Access-Control-Request-Headers', 'Content-Type,Authorization');

      expect(response.headers).toHaveProperty('access-control-allow-origin');
      expect(response.headers).toHaveProperty('access-control-allow-methods');
      expect(response.headers).toHaveProperty('access-control-allow-headers');
    });
  });
});