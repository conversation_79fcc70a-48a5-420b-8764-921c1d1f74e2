const Database = require('better-sqlite3');
const path = require('path');

const dbPath = './database/lifetracker.db';
console.log('Opening database at:', dbPath);

try {
  const db = new Database(dbPath);
  
  // Check if user_credentials table exists
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='user_credentials'").all();
  console.log('user_credentials table exists:', tables.length > 0);
  
  // Check if any users exist
  const users = db.prepare("SELECT * FROM user_credentials").all();
  console.log('Users in database:', users);
  
  // Check table schema
  const schema = db.prepare("PRAGMA table_info(user_credentials)").all();
  console.log('Table schema:', schema);
  
  db.close();
} catch (error) {
  console.error('Database error:', error);
}