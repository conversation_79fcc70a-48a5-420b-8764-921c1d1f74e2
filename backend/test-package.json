{"devDependencies": {"jest": "^29.7.0", "@types/jest": "^29.5.12", "supertest": "^7.0.0", "@types/supertest": "^6.0.2", "ts-jest": "^29.1.2"}, "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "transform": {"^.+\\.ts$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/**/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}