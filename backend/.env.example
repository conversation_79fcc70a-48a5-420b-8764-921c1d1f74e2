# LifeTracker Backend Environment Configuration

# Server Configuration
PORT=3001
NODE_ENV=production

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRY=5m

# Database Configuration
DATABASE_PATH=./database/lifetracker.db
DATABASE_BACKUP_PATH=./database/backups

# Security Configuration
BCRYPT_SALT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX=5

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Maintenance Configuration
VACUUM_INTERVAL_HOURS=168
ANALYZE_INTERVAL_HOURS=24