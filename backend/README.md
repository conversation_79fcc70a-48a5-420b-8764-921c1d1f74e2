# LifeTracker Backend

Express.js backend API for the LifeTracker personal task management application.

## Features

- **Secure Authentication**: JWT-based authentication with bcrypt password hashing
- **Task Management**: Full CRUD operations with recurring task support
- **Settings Management**: User preferences and application configuration
- **Database Management**: SQLite with better-sqlite3 for production reliability
- **Security**: Comprehensive security measures including rate limiting, CORS, input validation
- **Maintenance**: Automated database maintenance (VACUUM, ANALYZE) and backup operations
- **Health Monitoring**: System health checks and performance metrics
- **Production Ready**: 30-year operational design with graceful shutdown and error handling

## Quick Start

### Prerequisites

- Node.js 18+ 
- npm or yarn

### Installation

```bash
# Install dependencies
npm install

# Copy environment configuration
cp .env.example .env

# Build the application
npm run build

# Start development server
npm run dev

# Or start production server
npm start
```

### Environment Configuration

Create a `.env` file based on `.env.example`:

```bash
# Required - Change this in production!
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production

# Optional - Adjust as needed
PORT=3001
NODE_ENV=production
DATABASE_PATH=./database/lifetracker.db
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout  
- `GET /api/auth/session` - Session validation
- `POST /api/auth/refresh` - Token refresh

### Tasks
- `GET /api/tasks` - Get all tasks (with filtering)
- `GET /api/tasks/:id` - Get specific task
- `POST /api/tasks` - Create new task
- `PUT /api/tasks/:id` - Update task
- `DELETE /api/tasks/:id` - Delete task
- `POST /api/tasks/seed` - Seed initial tasks
- `GET /api/tasks/stats` - Task statistics

### Settings
- `GET /api/settings` - Get all settings
- `PUT /api/settings` - Update multiple settings
- `GET /api/settings/:key` - Get specific setting
- `PUT /api/settings/:key` - Update specific setting
- `DELETE /api/settings/:key` - Reset setting to default

### Health & Monitoring
- `GET /api/health` - Basic health check
- `GET /api/health/database` - Database health
- `GET /api/health/system` - System metrics
- `POST /api/health/maintenance` - Trigger maintenance
- `POST /api/health/backup` - Create backup

## Production Deployment

### Using PM2

```bash
# Install PM2 globally
npm install -g pm2

# Build application
npm run build

# Start with PM2
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
```

### Manual Deployment

```bash
# Build application
npm run build

# Set production environment
export NODE_ENV=production

# Start server
npm start
```

## Security Features

- **JWT Authentication**: 5-minute token expiration with refresh capability
- **Password Security**: Bcrypt hashing with 12 salt rounds
- **Rate Limiting**: Configurable limits for authentication and general API endpoints
- **Input Validation**: Comprehensive Joi schema validation
- **SQL Injection Prevention**: Parameterized queries with better-sqlite3
- **CORS Configuration**: Configurable origins for cross-origin requests
- **Security Headers**: Helmet.js with CSP, HSTS, and other security headers
- **Session Management**: In-memory session store with automatic cleanup

## Database

The application uses SQLite with better-sqlite3 for:

- **ACID Compliance**: Full transaction support
- **Performance**: Synchronous operations and prepared statements
- **Reliability**: Better-sqlite3 is more stable than sqlite3
- **Maintenance**: Automated VACUUM and ANALYZE operations
- **Backups**: Automated backup creation and cleanup

### Schema

- **tasks**: Task management with recurring task support
- **settings**: User preferences and application configuration  
- **user_credentials**: Single-user authentication (bcrypt hashed)
- **database_metadata**: Schema version and maintenance tracking

## Development

```bash
# Start development server with auto-reload
npm run dev

# Build TypeScript
npm run build

# Watch TypeScript compilation
npm run watch

# Clean build directory
npm run clean
```

## Architecture

The backend follows a layered architecture:

- **Routes**: API endpoint definitions and request handling
- **Middleware**: Authentication, validation, security, and error handling
- **Services**: Business logic and external integrations
- **Models**: Data structures and type definitions
- **Config**: Environment-based configuration management

## Error Handling

All API responses follow a consistent format:

```json
{
  "success": boolean,
  "data": any,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable message",
    "field": "field_name",
    "details": {}
  },
  "metadata": {
    "timestamp": "2025-08-03T10:30:00.000Z",
    "requestId": "uuid-here"
  }
}
```

## Maintenance

The application includes automated maintenance:

- **VACUUM**: Weekly database compaction (configurable)
- **ANALYZE**: Daily query optimizer statistics updates
- **Backups**: On-demand backup creation with integrity checks
- **Health Checks**: System and database health monitoring

## Contributing

1. Follow TypeScript strict mode requirements
2. Use Joi schemas for all input validation
3. Include comprehensive error handling
4. Add JSDoc comments for all public functions
5. Follow the existing architectural patterns

## License

MIT License - see LICENSE file for details.