/**
 * Location: /backend/src/config/index.ts
 * Purpose: Centralized configuration management for LifeTracker backend
 * Usage: Loads and validates environment variables, provides configuration objects
 */

import dotenv from 'dotenv';
import path from 'path';
import { AppConfig } from '../types';

// Load environment variables
dotenv.config();

// Validate required environment variables
const requiredEnvVars = ['JWT_SECRET'];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

// Parse CORS origins from comma-separated string
const parseCorsOrigins = (origins: string = ''): string[] => {
  return origins.split(',').map(origin => origin.trim()).filter(Boolean);
};

// Configuration object
export const config: AppConfig = {
  port: parseInt(process.env.PORT || '3001', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  
  database: {
    path: process.env.DATABASE_PATH || path.join(__dirname, '../../database/lifetracker.db'),
    backupPath: process.env.DATABASE_BACKUP_PATH || path.join(__dirname, '../../database/backups'),
    vacuumInterval: parseInt(process.env.VACUUM_INTERVAL_HOURS || '168', 10), // 1 week
    analyzeInterval: parseInt(process.env.ANALYZE_INTERVAL_HOURS || '24', 10), // 1 day
  },
  
  auth: {
    jwtSecret: process.env.JWT_SECRET!,
    jwtExpiry: process.env.JWT_EXPIRY || '5m',
    bcryptSaltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS || '12', 10),
    sessionTimeout: 5 * 60 * 1000, // 5 minutes in milliseconds
  },
  
  security: {
    rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW_MS || (process.env.NODE_ENV === 'development' ? '60000' : '900000'), 10), // 1 minute dev, 15 minutes prod
    rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || (process.env.NODE_ENV === 'development' ? '10000' : '1000'), 10),
    authRateLimitMax: parseInt(process.env.AUTH_RATE_LIMIT_MAX || '5', 10),
    corsOrigins: parseCorsOrigins(process.env.CORS_ORIGINS || 'http://localhost:3000,http://localhost:5173'),
  },
};

// Validation
if (config.auth.bcryptSaltRounds < 10 || config.auth.bcryptSaltRounds > 15) {
  throw new Error('BCRYPT_SALT_ROUNDS must be between 10 and 15');
}

if (config.port < 1024 || config.port > 65535) {
  throw new Error('PORT must be between 1024 and 65535');
}

// Export individual config sections for convenience
export const { port, nodeEnv } = config;
export const { database, auth, security } = config;

export default config;