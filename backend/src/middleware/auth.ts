/**
 * Location: /backend/src/middleware/auth.ts
 * Purpose: JWT authentication middleware for protected routes
 * Usage: Validates JWT tokens and adds user information to request object
 */

import { Request, Response, NextFunction } from 'express';
import { authService } from '../services/authService';
import { AuthenticatedRequest } from '../types';

/**
 * Authentication middleware to protect routes
 */
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Extract token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_REQUIRED',
          message: 'Authentication token required',
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      });
      return;
    }

    const token = authHeader.substring(7); // Remove "Bearer " prefix

    // Validate JWT token
    const payload = authService.validateToken(token);

    // Add user information to request
    req.user = {
      id: parseInt(payload.sub),
      username: payload.username,
      tokenId: payload.jti,
    };

    next();
  } catch (error: any) {
    let errorCode = 'AUTH_TOKEN_INVALID';
    let errorMessage = 'Invalid or expired authentication token';

    if (error.message.includes('expired')) {
      errorCode = 'AUTH_TOKEN_EXPIRED';
      errorMessage = 'Authentication token has expired';
    } else if (error.message.includes('Session')) {
      errorCode = 'AUTH_SESSION_INVALID';
      errorMessage = 'Session is no longer valid';
    }

    res.status(401).json({
      success: false,
      error: {
        code: errorCode,
        message: errorMessage,
      },
      metadata: {
        timestamp: new Date().toISOString(),
        requestId: req.headers['x-request-id'] as string || 'unknown',
      },
    });
  }
};

/**
 * Optional authentication middleware for routes that can work with or without auth
 */
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      
      try {
        const payload = authService.validateToken(token);
        req.user = {
          id: parseInt(payload.sub),
          username: payload.username,
          tokenId: payload.jti,
        };
      } catch (error) {
        // Ignore errors for optional auth
      }
    }
    
    next();
  } catch (error) {
    // Always continue for optional auth
    next();
  }
};