/**
 * Location: /backend/src/middleware/errorHandler.ts
 * Purpose: Global error handling middleware for consistent error responses
 * Usage: Catches and formats all application errors with proper HTTP status codes
 */

import { Request, Response, NextFunction } from 'express';
import { config } from '../config';
import { AppError } from '../types';

/**
 * Custom application error class
 */
export class ApplicationError extends Error implements AppError {
  public statusCode: number;
  public code: string;
  public field?: string;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR',
    field?: string,
    details?: any
  ) {
    super(message);
    this.name = 'ApplicationError';
    this.statusCode = statusCode;
    this.code = code;
    this.field = field;
    this.details = details;

    // Ensure the stack trace points to where the error was thrown
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Create standardized error responses
 */
export const createErrorResponse = (
  error: AppError | Error,
  requestId: string = 'unknown'
) => {
  const isAppError = error instanceof ApplicationError;
  
  return {
    success: false,
    error: {
      code: isAppError ? error.code : 'INTERNAL_ERROR',
      message: error.message,
      ...(isAppError && error.field && { field: error.field }),
      ...(isAppError && error.details && { details: error.details }),
    },
    metadata: {
      timestamp: new Date().toISOString(),
      requestId,
    },
  };
};

/**
 * Global error handling middleware
 */
export const errorHandler = (
  error: Error | ApplicationError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const requestId = req.headers['x-request-id'] as string || 'unknown';
  
  // Log error in development
  if (config.nodeEnv === 'development') {
    console.error(`Error [${requestId}]:`, error);
  }

  // Handle different types of errors
  if (error instanceof ApplicationError) {
    res.status(error.statusCode).json(createErrorResponse(error, requestId));
    return;
  }

  // Handle validation errors from Joi (should be caught by validation middleware)
  if (error.name === 'ValidationError') {
    res.status(400).json(createErrorResponse(
      new ApplicationError(
        'Validation failed',
        400,
        'VALIDATION_ERROR',
        undefined,
        error.message
      ),
      requestId
    ));
    return;
  }

  // Handle JWT errors
  if (error.name === 'JsonWebTokenError') {
    res.status(401).json(createErrorResponse(
      new ApplicationError(
        'Invalid authentication token',
        401,
        'AUTH_TOKEN_INVALID'
      ),
      requestId
    ));
    return;
  }

  if (error.name === 'TokenExpiredError') {
    res.status(401).json(createErrorResponse(
      new ApplicationError(
        'Authentication token has expired',
        401,
        'AUTH_TOKEN_EXPIRED'
      ),
      requestId
    ));
    return;
  }

  // Handle database errors
  if (error.message.includes('SQLITE_')) {
    let statusCode = 500;
    let code = 'DATABASE_ERROR';
    let message = 'Database operation failed';

    if (error.message.includes('SQLITE_CONSTRAINT')) {
      statusCode = 400;
      code = 'CONSTRAINT_VIOLATION';
      message = 'Data constraint violation';
    } else if (error.message.includes('SQLITE_BUSY')) {
      statusCode = 503;
      code = 'DATABASE_BUSY';
      message = 'Database is temporarily busy';
    }

    res.status(statusCode).json(createErrorResponse(
      new ApplicationError(message, statusCode, code),
      requestId
    ));
    return;
  }

  // Handle CORS errors
  if (error.message.includes('CORS')) {
    res.status(403).json(createErrorResponse(
      new ApplicationError(
        'CORS policy violation',
        403,
        'CORS_ERROR'
      ),
      requestId
    ));
    return;
  }

  // Handle syntax errors (malformed JSON)
  if (error instanceof SyntaxError && 'body' in error) {
    res.status(400).json(createErrorResponse(
      new ApplicationError(
        'Invalid JSON in request body',
        400,
        'INVALID_JSON'
      ),
      requestId
    ));
    return;
  }

  // Handle rate limit errors (should be handled by rate limit middleware)
  if (error.message.includes('Too many requests')) {
    res.status(429).json(createErrorResponse(
      new ApplicationError(
        'Rate limit exceeded',
        429,
        'RATE_LIMIT_EXCEEDED'
      ),
      requestId
    ));
    return;
  }

  // Default error response for unknown errors
  const statusCode = 500;
  const message = config.nodeEnv === 'development' 
    ? error.message 
    : 'An unexpected error occurred';

  res.status(statusCode).json(createErrorResponse(
    new ApplicationError(message, statusCode, 'INTERNAL_ERROR'),
    requestId
  ));
};

/**
 * 404 Not Found handler
 */
export const notFoundHandler = (req: Request, res: Response): void => {
  const requestId = req.headers['x-request-id'] as string || 'unknown';
  
  res.status(404).json(createErrorResponse(
    new ApplicationError(
      `Route ${req.method} ${req.originalUrl} not found`,
      404,
      'ROUTE_NOT_FOUND'
    ),
    requestId
  ));
};

/**
 * Async error wrapper for route handlers
 */
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<any>
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Common error factory functions
 */
export const errors = {
  notFound: (resource: string) => 
    new ApplicationError(`${resource} not found`, 404, 'RESOURCE_NOT_FOUND'),
  
  unauthorized: (message: string = 'Unauthorized') =>
    new ApplicationError(message, 401, 'UNAUTHORIZED'),
  
  forbidden: (message: string = 'Forbidden') =>
    new ApplicationError(message, 403, 'FORBIDDEN'),
  
  badRequest: (message: string, field?: string) =>
    new ApplicationError(message, 400, 'BAD_REQUEST', field),
  
  conflict: (message: string) =>
    new ApplicationError(message, 409, 'CONFLICT'),
  
  internal: (message: string = 'Internal server error') =>
    new ApplicationError(message, 500, 'INTERNAL_ERROR'),
  
  serviceUnavailable: (message: string = 'Service temporarily unavailable') =>
    new ApplicationError(message, 503, 'SERVICE_UNAVAILABLE'),
};

/**
 * Success response helper
 */
export const successResponse = <T>(
  res: Response,
  data: T,
  statusCode: number = 200,
  requestId: string = 'unknown',
  pagination?: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
  }
) => {
  const response: any = {
    success: true,
    data,
    metadata: {
      timestamp: new Date().toISOString(),
      requestId,
      ...(pagination && { pagination }),
    },
  };

  res.status(statusCode).json(response);
};