/**
 * Location: /backend/src/middleware/validation.ts
 * Purpose: Input validation middleware using Joi schemas
 * Usage: Validates request bodies, query parameters, and path parameters
 */

import { Request, Response, NextFunction } from 'express';
import Jo<PERSON> from 'joi';
import { Priority, RecurrenceType } from '../types';

/**
 * Generic validation middleware factory
 */
export const validate = (schema: Joi.ObjectSchema, property: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req[property], { 
      abortEarly: false, // Return all validation errors
      stripUnknown: true, // Remove unknown properties
    });

    if (error) {
      const validationErrors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));

      res.status(400).json({
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid request data',
          details: validationErrors,
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId: req.headers['x-request-id'] as string || 'unknown',
        },
      });
      return;
    }

    next();
  };
};

// Authentication validation schemas
export const loginSchema = Joi.object({
  username: Joi.string()
    .min(3)
    .max(50)
    .pattern(/^[a-zA-Z0-9_]+$/)
    .required()
    .messages({
      'string.pattern.base': 'Username can only contain letters, numbers, and underscores',
      'string.min': 'Username must be at least 3 characters',
      'string.max': 'Username cannot exceed 50 characters',
    }),
  password: Joi.string()
    .min(8)
    .max(128)
    .required()
    .messages({
      'string.min': 'Password must be at least 8 characters',
      'string.max': 'Password cannot exceed 128 characters',
    }),
});

// Task validation schemas
export const createTaskSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(200)
    .required()
    .messages({
      'string.min': 'Task title cannot be empty',
      'string.max': 'Task title cannot exceed 200 characters',
    }),
  description: Joi.string()
    .max(1000)
    .allow('', null)
    .optional()
    .messages({
      'string.max': 'Task description cannot exceed 1000 characters',
    }),
  dueDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .required()
    .custom((value, helpers) => {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return helpers.error('any.invalid');
      }
      return value;
    })
    .messages({
      'string.pattern.base': 'Due date must be in YYYY-MM-DD format',
      'any.invalid': 'Due date must be a valid date',
    }),
  dueTime: Joi.string()
    .pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .required()
    .messages({
      'string.pattern.base': 'Due time must be in HH:mm format (24-hour)',
    }),
  priority: Joi.string()
    .valid(...Object.values(Priority))
    .required()
    .messages({
      'any.only': 'Priority must be Low, Medium, or High',
    }),
  category: Joi.string()
    .max(50)
    .allow('', null)
    .optional()
    .messages({
      'string.max': 'Category cannot exceed 50 characters',
    }),
  isRecurring: Joi.boolean()
    .optional()
    .default(false),
  recurrenceType: Joi.string()
    .valid(...Object.values(RecurrenceType))
    .when('isRecurring', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional().allow(null),
    })
    .messages({
      'any.only': 'Recurrence type must be Daily, Weekly, Monthly, or Yearly',
      'any.required': 'Recurrence type is required when task is recurring',
    }),
  recurrenceInterval: Joi.number()
    .integer()
    .min(1)
    .max(365)
    .when('isRecurring', {
      is: true,
      then: Joi.optional().default(1),
      otherwise: Joi.optional().allow(null),
    })
    .messages({
      'number.min': 'Recurrence interval must be at least 1',
      'number.max': 'Recurrence interval cannot exceed 365',
    }),
});

export const updateTaskSchema = Joi.object({
  title: Joi.string()
    .min(1)
    .max(200)
    .optional()
    .messages({
      'string.min': 'Task title cannot be empty',
      'string.max': 'Task title cannot exceed 200 characters',
    }),
  description: Joi.string()
    .max(1000)
    .allow('', null)
    .optional()
    .messages({
      'string.max': 'Task description cannot exceed 1000 characters',
    }),
  dueDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .optional()
    .custom((value, helpers) => {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return helpers.error('any.invalid');
      }
      return value;
    })
    .messages({
      'string.pattern.base': 'Due date must be in YYYY-MM-DD format',
      'any.invalid': 'Due date must be a valid date',
    }),
  dueTime: Joi.string()
    .pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .optional()
    .messages({
      'string.pattern.base': 'Due time must be in HH:mm format (24-hour)',
    }),
  isCompleted: Joi.boolean()
    .optional(),
  priority: Joi.string()
    .valid(...Object.values(Priority))
    .optional()
    .messages({
      'any.only': 'Priority must be Low, Medium, or High',
    }),
  category: Joi.string()
    .max(50)
    .allow('', null)
    .optional()
    .messages({
      'string.max': 'Category cannot exceed 50 characters',
    }),
  isRecurring: Joi.boolean()
    .optional(),
  recurrenceType: Joi.string()
    .valid(...Object.values(RecurrenceType))
    .allow(null)
    .optional()
    .messages({
      'any.only': 'Recurrence type must be Daily, Weekly, Monthly, or Yearly',
    }),
  recurrenceInterval: Joi.number()
    .integer()
    .min(1)
    .max(365)
    .allow(null)
    .optional()
    .messages({
      'number.min': 'Recurrence interval must be at least 1',
      'number.max': 'Recurrence interval cannot exceed 365',
    }),
});

// Task query validation schema
export const taskQuerySchema = Joi.object({
  completed: Joi.boolean()
    .optional(),
  category: Joi.string()
    .max(50)
    .optional(),
  priority: Joi.string()
    .valid(...Object.values(Priority))
    .optional(),
  dueDate: Joi.string()
    .pattern(/^\d{4}-\d{2}-\d{2}$/)
    .optional()
    .custom((value, helpers) => {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return helpers.error('any.invalid');
      }
      return value;
    }),
  search: Joi.string()
    .max(200)
    .optional(),
  limit: Joi.number()
    .integer()
    .min(1)
    .max(1000)
    .optional()
    .default(100),
  offset: Joi.number()
    .integer()
    .min(0)
    .optional()
    .default(0),
});

// Settings validation schemas
export const updateSettingsSchema = Joi.object({
  showCompleted: Joi.boolean()
    .optional(),
  seeded: Joi.boolean()
    .optional(),
}).min(1); // At least one property must be provided

export const updateSettingSchema = Joi.object({
  value: Joi.alternatives()
    .try(
      Joi.boolean(),
      Joi.string().max(1000),
      Joi.number()
    )
    .required()
    .messages({
      'alternatives.match': 'Setting value must be a boolean, string, or number',
      'string.max': 'String values cannot exceed 1000 characters',
    }),
});

// Path parameter validation schemas
export const taskIdSchema = Joi.object({
  id: Joi.string()
    .uuid()
    .required()
    .messages({
      'string.uuid': 'Task ID must be a valid UUID',
    }),
});

export const settingKeySchema = Joi.object({
  key: Joi.string()
    .valid('showCompleted', 'seeded', 'theme', 'language', 'timezone')
    .required()
    .messages({
      'any.only': 'Setting key must be one of: showCompleted, seeded, theme, language, timezone',
    }),
});

// Validation middleware functions
export const validateLogin = validate(loginSchema);
export const validateCreateTask = validate(createTaskSchema);
export const validateUpdateTask = validate(updateTaskSchema);
export const validateTaskQuery = validate(taskQuerySchema, 'query');
export const validateUpdateSettings = validate(updateSettingsSchema);
export const validateUpdateSetting = validate(updateSettingSchema);
export const validateTaskId = validate(taskIdSchema, 'params');
export const validateSettingKey = validate(settingKeySchema, 'params');