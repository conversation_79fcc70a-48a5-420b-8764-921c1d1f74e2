/**
 * Location: /backend/src/services/maintenanceService.ts
 * Purpose: Database maintenance service with automated VACUUM and ANALYZE operations
 * Usage: Handles scheduled database maintenance tasks for optimal performance
 */

import fs from 'fs';
import path from 'path';
import { config } from '../config';
import { dbService } from './dbService';

class MaintenanceService {
  private vacuumInterval: NodeJS.Timeout | null = null;
  private analyzeInterval: NodeJS.Timeout | null = null;
  private isMaintenanceRunning = false;

  /**
   * Initialize maintenance service with scheduled tasks
   */
  async initialize(): Promise<void> {
    try {
      // Schedule VACUUM operations (weekly)
      const vacuumIntervalMs = config.database.vacuumInterval * 60 * 60 * 1000; // Convert hours to milliseconds
      this.vacuumInterval = setInterval(() => {
        this.runVacuumMaintenance();
      }, vacuumIntervalMs);

      // Schedule ANALYZE operations (daily)
      const analyzeIntervalMs = config.database.analyzeInterval * 60 * 60 * 1000; // Convert hours to milliseconds
      this.analyzeInterval = setInterval(() => {
        this.runAnalyzeMaintenance();
      }, analyzeIntervalMs);

      // Run initial maintenance check
      await this.checkAndRunMaintenance();

      if (config.nodeEnv === 'development') {
        console.log('Maintenance service initialized successfully');
      }
    } catch (error) {
      throw new Error(`Failed to initialize maintenance service: ${error}`);
    }
  }

  /**
   * Run full database maintenance (VACUUM + ANALYZE)
   */
  async runFullMaintenance(): Promise<void> {
    if (this.isMaintenanceRunning) {
      throw new Error('Maintenance is already running');
    }

    this.isMaintenanceRunning = true;

    try {
      if (config.nodeEnv === 'development') {
        console.log('Starting full database maintenance...');
      }

      await dbService.runMaintenance();

      if (config.nodeEnv === 'development') {
        console.log('Full database maintenance completed successfully');
      }
    } catch (error) {
      throw new Error(`Database maintenance failed: ${error}`);
    } finally {
      this.isMaintenanceRunning = false;
    }
  }

  /**
   * Run VACUUM operation to reclaim disk space
   */
  async runVacuumMaintenance(): Promise<void> {
    if (this.isMaintenanceRunning) {
      return; // Skip if maintenance is already running
    }

    this.isMaintenanceRunning = true;

    try {
      if (config.nodeEnv === 'development') {
        console.log('Running VACUUM maintenance...');
      }

      // Get database size before VACUUM
      const statsBefore = await dbService.getStats();

      await dbService.runMaintenance();

      // Get database size after VACUUM
      const statsAfter = await dbService.getStats();
      const spaceSaved = statsBefore.size - statsAfter.size;

      if (config.nodeEnv === 'development') {
        console.log(`VACUUM completed. Space reclaimed: ${this.formatBytes(spaceSaved)}`);
      }
    } catch (error) {
      console.error('VACUUM maintenance failed:', error);
    } finally {
      this.isMaintenanceRunning = false;
    }
  }

  /**
   * Run ANALYZE operation to update query planner statistics
   */
  async runAnalyzeMaintenance(): Promise<void> {
    if (this.isMaintenanceRunning) {
      return; // Skip if maintenance is already running
    }

    this.isMaintenanceRunning = true;

    try {
      if (config.nodeEnv === 'development') {
        console.log('Running ANALYZE maintenance...');
      }

      // Note: dbService.runMaintenance() includes both VACUUM and ANALYZE
      // For ANALYZE-only, we would need a separate method, but for simplicity
      // we use the full maintenance which is safe to run frequently
      await dbService.runMaintenance();

      if (config.nodeEnv === 'development') {
        console.log('ANALYZE maintenance completed');
      }
    } catch (error) {
      console.error('ANALYZE maintenance failed:', error);
    } finally {
      this.isMaintenanceRunning = false;
    }
  }

  /**
   * Check database integrity
   */
  async checkDatabaseIntegrity(): Promise<{ isHealthy: boolean; message: string }> {
    try {
      const isHealthy = await dbService.checkIntegrity();
      
      return {
        isHealthy,
        message: isHealthy ? 'Database integrity check passed' : 'Database integrity check failed',
      };
    } catch (error) {
      return {
        isHealthy: false,
        message: `Database integrity check error: ${error}`,
      };
    }
  }

  /**
   * Create database backup
   */
  async createBackup(): Promise<string> {
    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupFileName = `lifetracker_backup_${timestamp}.db`;
      const backupPath = path.join(config.database.backupPath, backupFileName);

      // Ensure backup directory exists
      if (!fs.existsSync(config.database.backupPath)) {
        fs.mkdirSync(config.database.backupPath, { recursive: true });
      }

      // Copy database file
      fs.copyFileSync(config.database.path, backupPath);

      // Verify backup integrity
      const Database = require('better-sqlite3');
      const backupDb = new Database(backupPath, { readonly: true });
      const result = backupDb.prepare('PRAGMA integrity_check').get() as { integrity_check: string };
      backupDb.close();

      if (result.integrity_check !== 'ok') {
        fs.unlinkSync(backupPath); // Remove corrupted backup
        throw new Error('Backup integrity check failed');
      }

      if (config.nodeEnv === 'development') {
        console.log(`Database backup created: ${backupPath}`);
      }

      return backupPath;
    } catch (error) {
      throw new Error(`Failed to create database backup: ${error}`);
    }
  }

  /**
   * Clean up old backup files
   */
  async cleanupOldBackups(retentionDays: number = 30): Promise<number> {
    try {
      if (!fs.existsSync(config.database.backupPath)) {
        return 0;
      }

      const files = fs.readdirSync(config.database.backupPath);
      const backupFiles = files.filter(file => file.startsWith('lifetracker_backup_') && file.endsWith('.db'));
      
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

      let deletedCount = 0;

      for (const file of backupFiles) {
        const filePath = path.join(config.database.backupPath, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          deletedCount++;
        }
      }

      if (config.nodeEnv === 'development' && deletedCount > 0) {
        console.log(`Cleaned up ${deletedCount} old backup files`);
      }

      return deletedCount;
    } catch (error) {
      console.error('Failed to clean up old backups:', error);
      return 0;
    }
  }

  /**
   * Get database statistics and health information
   */
  async getDatabaseStats(): Promise<{
    stats: { taskCount: number; completedTaskCount: number; settingsCount: number; size: number };
    health: { isHealthy: boolean; message: string };
    backupInfo: { lastBackup: string | null; backupCount: number };
  }> {
    try {
      const stats = await dbService.getStats();
      const health = await this.checkDatabaseIntegrity();
      const backupInfo = this.getBackupInfo();

      return {
        stats,
        health,
        backupInfo,
      };
    } catch (error) {
      throw new Error(`Failed to get database statistics: ${error}`);
    }
  }

  /**
   * Get backup information
   */
  private getBackupInfo(): { lastBackup: string | null; backupCount: number } {
    try {
      if (!fs.existsSync(config.database.backupPath)) {
        return { lastBackup: null, backupCount: 0 };
      }

      const files = fs.readdirSync(config.database.backupPath);
      const backupFiles = files.filter(file => file.startsWith('lifetracker_backup_') && file.endsWith('.db'));

      if (backupFiles.length === 0) {
        return { lastBackup: null, backupCount: 0 };
      }

      // Sort by modification time to get the latest backup
      const backupStats = backupFiles.map(file => {
        const filePath = path.join(config.database.backupPath, file);
        const stats = fs.statSync(filePath);
        return { file, mtime: stats.mtime };
      });

      backupStats.sort((a, b) => b.mtime.getTime() - a.mtime.getTime());
      const lastBackup = backupStats[0].mtime.toISOString();

      return {
        lastBackup,
        backupCount: backupFiles.length,
      };
    } catch (error) {
      return { lastBackup: null, backupCount: 0 };
    }
  }

  /**
   * Check if maintenance should be run based on last run time
   */
  private async checkAndRunMaintenance(): Promise<void> {
    try {
      // This is a simple check - in a real implementation, you might want to
      // store the last maintenance time in the database_metadata table
      // and check if it's time to run maintenance based on the configured intervals
      
      // For now, we'll just ensure the service is ready
      if (config.nodeEnv === 'development') {
        console.log('Maintenance service ready for scheduled operations');
      }
    } catch (error) {
      console.error('Failed to check maintenance schedule:', error);
    }
  }

  /**
   * Format bytes to human readable string
   */
  private formatBytes(bytes: number, decimals: number = 2): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }

  /**
   * Stop maintenance service
   */
  stop(): void {
    if (this.vacuumInterval) {
      clearInterval(this.vacuumInterval);
      this.vacuumInterval = null;
    }

    if (this.analyzeInterval) {
      clearInterval(this.analyzeInterval);
      this.analyzeInterval = null;
    }

    if (config.nodeEnv === 'development') {
      console.log('Maintenance service stopped');
    }
  }

  /**
   * Get maintenance status
   */
  getStatus(): {
    isRunning: boolean;
    vacuumScheduled: boolean;
    analyzeScheduled: boolean;
    vacuumInterval: number;
    analyzeInterval: number;
  } {
    return {
      isRunning: this.isMaintenanceRunning,
      vacuumScheduled: this.vacuumInterval !== null,
      analyzeScheduled: this.analyzeInterval !== null,
      vacuumInterval: config.database.vacuumInterval,
      analyzeInterval: config.database.analyzeInterval,
    };
  }
}

// Export singleton instance
export const maintenanceService = new MaintenanceService();
export default maintenanceService;