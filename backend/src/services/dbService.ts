/**
 * Location: /backend/src/services/dbService.ts
 * Purpose: SQLite database service using better-sqlite3 for production reliability
 * Usage: Centralized database operations with transaction support and maintenance
 */

import Database from 'better-sqlite3';
import fs from 'fs';
import path from 'path';
import { config } from '../config';
import { Task, Settings, Priority, RecurrenceType, CreateTaskRequest, UpdateTaskRequest, TaskFilters } from '../types';

class DatabaseService {
  private db: Database.Database | null = null;
  private isInitialized = false;

  /**
   * Initialize the database connection and create schema if needed
   */
  async initialize(): Promise<void> {
    try {
      // Ensure database directory exists
      const dbDir = path.dirname(config.database.path);
      if (!fs.existsSync(dbDir)) {
        fs.mkdirSync(dbDir, { recursive: true });
      }

      // Ensure backup directory exists
      if (!fs.existsSync(config.database.backupPath)) {
        fs.mkdirSync(config.database.backupPath, { recursive: true });
      }

      // Open database connection
      this.db = new Database(config.database.path, {
        verbose: config.nodeEnv === 'development' ? console.log : undefined,
      });

      // Configure SQLite for optimal performance and reliability
      this.db.pragma('journal_mode = WAL');
      this.db.pragma('synchronous = NORMAL');
      this.db.pragma('cache_size = -64000'); // 64MB cache
      this.db.pragma('temp_store = MEMORY');
      this.db.pragma('mmap_size = 268435456'); // 256MB mmap

      // Create schema if it doesn't exist
      await this.createSchema();
      
      // Initialize default data
      await this.initializeDefaultData();

      this.isInitialized = true;
      
      if (config.nodeEnv === 'development') {
        console.log('Database initialized successfully');
      }
    } catch (error) {
      throw new Error(`Failed to initialize database: ${error}`);
    }
  }

  /**
   * Create database schema with indexes and constraints
   */
  private async createSchema(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Create tasks table with comprehensive constraints
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY NOT NULL,
        title TEXT NOT NULL CHECK (length(title) > 0 AND length(title) <= 200),
        description TEXT CHECK (description IS NULL OR length(description) <= 1000),
        dueDate TEXT NOT NULL CHECK (date(dueDate) IS NOT NULL),
        dueTime TEXT NOT NULL CHECK (time(dueTime) IS NOT NULL),
        isCompleted INTEGER NOT NULL DEFAULT 0 CHECK (isCompleted IN (0, 1)),
        completedAt TEXT CHECK (completedAt IS NULL OR datetime(completedAt) IS NOT NULL),
        priority TEXT NOT NULL CHECK (priority IN ('Low', 'Medium', 'High')),
        category TEXT CHECK (category IS NULL OR (length(category) > 0 AND length(category) <= 50)),
        isRecurring INTEGER NOT NULL DEFAULT 0 CHECK (isRecurring IN (0, 1)),
        recurrenceType TEXT CHECK (
          recurrenceType IS NULL OR 
          recurrenceType IN ('Daily', 'Weekly', 'Monthly', 'Yearly')
        ),
        recurrenceInterval INTEGER CHECK (
          recurrenceInterval IS NULL OR 
          (recurrenceInterval >= 1 AND recurrenceInterval <= 365)
        ),
        createdAt TEXT NOT NULL DEFAULT (datetime('now')) 
          CHECK (datetime(createdAt) IS NOT NULL),
        updatedAt TEXT NOT NULL DEFAULT (datetime('now')) 
          CHECK (datetime(updatedAt) IS NOT NULL),
        
        CONSTRAINT valid_recurrence_config CHECK (
          (isRecurring = 0 AND recurrenceType IS NULL AND recurrenceInterval IS NULL) OR
          (isRecurring = 1 AND recurrenceType IS NOT NULL)
        ),
        CONSTRAINT valid_completion CHECK (
          (isCompleted = 0 AND completedAt IS NULL) OR
          (isCompleted = 1 AND completedAt IS NOT NULL)
        )
      );
    `);

    // Create settings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY NOT NULL CHECK (length(key) > 0 AND length(key) <= 50),
        value TEXT NOT NULL,
        updatedAt TEXT NOT NULL DEFAULT (datetime('now')) 
          CHECK (datetime(updatedAt) IS NOT NULL)
      );
    `);

    // Create user credentials table (single user)
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_credentials (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        username TEXT NOT NULL UNIQUE CHECK (length(username) >= 3 AND length(username) <= 50),
        password_hash TEXT NOT NULL CHECK (length(password_hash) = 60),
        last_login TEXT CHECK (last_login IS NULL OR datetime(last_login) IS NOT NULL),
        login_attempts INTEGER NOT NULL DEFAULT 0 CHECK (login_attempts >= 0),
        locked_until TEXT CHECK (locked_until IS NULL OR datetime(locked_until) IS NOT NULL),
        created_at TEXT NOT NULL DEFAULT (datetime('now')) 
          CHECK (datetime(created_at) IS NOT NULL),
        updated_at TEXT NOT NULL DEFAULT (datetime('now')) 
          CHECK (datetime(updated_at) IS NOT NULL)
      );
    `);

    // Create database metadata table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS database_metadata (
        id INTEGER PRIMARY KEY CHECK (id = 1),
        schema_version TEXT NOT NULL DEFAULT '1.0.0',
        last_vacuum TEXT CHECK (last_vacuum IS NULL OR datetime(last_vacuum) IS NOT NULL),
        last_analyze TEXT CHECK (last_analyze IS NULL OR datetime(last_analyze) IS NOT NULL),
        last_integrity_check TEXT CHECK (last_integrity_check IS NULL OR datetime(last_integrity_check) IS NOT NULL),
        total_tasks_created INTEGER NOT NULL DEFAULT 0 CHECK (total_tasks_created >= 0),
        total_tasks_completed INTEGER NOT NULL DEFAULT 0 CHECK (total_tasks_completed >= 0),
        created_at TEXT NOT NULL DEFAULT (datetime('now')) 
          CHECK (datetime(created_at) IS NOT NULL),
        updated_at TEXT NOT NULL DEFAULT (datetime('now')) 
          CHECK (datetime(updated_at) IS NOT NULL)
      );
    `);

    // Create performance indexes
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_tasks_due_date ON tasks(dueDate);
      CREATE INDEX IF NOT EXISTS idx_tasks_completed ON tasks(isCompleted);
      CREATE INDEX IF NOT EXISTS idx_tasks_priority ON tasks(priority);
      CREATE INDEX IF NOT EXISTS idx_tasks_category ON tasks(category) WHERE category IS NOT NULL;
      CREATE INDEX IF NOT EXISTS idx_tasks_recurring ON tasks(isRecurring) WHERE isRecurring = 1;
      CREATE INDEX IF NOT EXISTS idx_tasks_due_completed ON tasks(dueDate, isCompleted);
      CREATE INDEX IF NOT EXISTS idx_tasks_category_completed ON tasks(category, isCompleted) WHERE category IS NOT NULL;
      CREATE INDEX IF NOT EXISTS idx_tasks_priority_due ON tasks(priority, dueDate);
      CREATE INDEX IF NOT EXISTS idx_tasks_title ON tasks(title);
      CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(createdAt);
      CREATE INDEX IF NOT EXISTS idx_tasks_updated_at ON tasks(updatedAt);
      CREATE INDEX IF NOT EXISTS idx_settings_updated_at ON settings(updatedAt);
      CREATE UNIQUE INDEX IF NOT EXISTS idx_user_username ON user_credentials(username);
      CREATE INDEX IF NOT EXISTS idx_user_last_login ON user_credentials(last_login);
    `);

    // Create triggers for automatic timestamp updates
    this.db.exec(`
      CREATE TRIGGER IF NOT EXISTS trigger_tasks_updated_at
        AFTER UPDATE ON tasks
        FOR EACH ROW
      BEGIN
        UPDATE tasks SET updatedAt = datetime('now') WHERE id = NEW.id;
      END;

      CREATE TRIGGER IF NOT EXISTS trigger_settings_updated_at
        AFTER UPDATE ON settings
        FOR EACH ROW
      BEGIN
        UPDATE settings SET updatedAt = datetime('now') WHERE key = NEW.key;
      END;

      CREATE TRIGGER IF NOT EXISTS trigger_user_updated_at
        AFTER UPDATE ON user_credentials
        FOR EACH ROW
      BEGIN
        UPDATE user_credentials SET updated_at = datetime('now') WHERE id = NEW.id;
      END;

      CREATE TRIGGER IF NOT EXISTS trigger_task_created_stats
        AFTER INSERT ON tasks
        FOR EACH ROW
      BEGIN
        INSERT OR IGNORE INTO database_metadata (id, schema_version) VALUES (1, '1.0.0');
        UPDATE database_metadata 
        SET total_tasks_created = total_tasks_created + 1,
            updated_at = datetime('now')
        WHERE id = 1;
      END;

      CREATE TRIGGER IF NOT EXISTS trigger_task_completed_stats
        AFTER UPDATE OF isCompleted ON tasks
        FOR EACH ROW
        WHEN NEW.isCompleted = 1 AND OLD.isCompleted = 0
      BEGIN
        UPDATE database_metadata 
        SET total_tasks_completed = total_tasks_completed + 1,
            updated_at = datetime('now')
        WHERE id = 1;
      END;
    `);
  }

  /**
   * Initialize default data and settings
   */
  private async initializeDefaultData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Initialize database metadata
    const insertMetadata = this.db.prepare(`
      INSERT OR IGNORE INTO database_metadata (id, schema_version) VALUES (1, '1.0.0')
    `);
    insertMetadata.run();

    // Initialize default settings
    const insertSetting = this.db.prepare(`
      INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)
    `);
    
    const defaultSettings = [
      ['showCompleted', 'true'],
      ['seeded', 'false'],
    ];

    for (const [key, value] of defaultSettings) {
      insertSetting.run(key, value);
    }

    // Initialize placeholder user credentials (will be updated by auth service)
    const insertUser = this.db.prepare(`
      INSERT OR IGNORE INTO user_credentials (id, username, password_hash) 
      VALUES (1, 'scharway', '$2b$12$placeholder.hash.that.is.exactly.60.chars.abcdefghijk')
    `);
    insertUser.run();
  }

  /**
   * Run a function within a database transaction
   */
  async runInTransaction<T>(callback: () => T): Promise<T> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction(() => {
      return callback();
    });

    return transaction();
  }

  /**
   * Get all tasks with optional filtering
   */
  async getTasks(filters?: TaskFilters): Promise<Task[]> {
    if (!this.db) throw new Error('Database not initialized');

    let query = 'SELECT * FROM tasks WHERE 1=1';
    const params: any[] = [];

    if (filters?.completed !== undefined) {
      query += ' AND isCompleted = ?';
      params.push(filters.completed ? 1 : 0);
    }

    if (filters?.category) {
      query += ' AND category = ?';
      params.push(filters.category);
    }

    if (filters?.priority) {
      query += ' AND priority = ?';
      params.push(filters.priority);
    }

    if (filters?.dueDate) {
      query += ' AND dueDate = ?';
      params.push(filters.dueDate);
    }

    if (filters?.search) {
      query += ' AND (title LIKE ? OR description LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
    }

    query += ' ORDER BY dueDate, dueTime';

    if (filters?.limit) {
      query += ' LIMIT ?';
      params.push(filters.limit);
      
      if (filters?.offset) {
        query += ' OFFSET ?';
        params.push(filters.offset);
      }
    }

    const stmt = this.db.prepare(query);
    const rows = stmt.all(...params) as any[];

    return rows.map(this.rowToTask);
  }

  /**
   * Get a single task by ID
   */
  async getTaskById(id: string): Promise<Task | null> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare('SELECT * FROM tasks WHERE id = ?');
    const row = stmt.get(id) as any;

    return row ? this.rowToTask(row) : null;
  }

  /**
   * Add a new task
   */
  async addTask(taskData: CreateTaskRequest): Promise<Task> {
    if (!this.db) throw new Error('Database not initialized');

    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const stmt = this.db.prepare(`
      INSERT INTO tasks (
        id, title, description, dueDate, dueTime, priority, category,
        isRecurring, recurrenceType, recurrenceInterval, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      id,
      taskData.title,
      taskData.description || null,
      taskData.dueDate,
      taskData.dueTime,
      taskData.priority,
      taskData.category || null,
      taskData.isRecurring ? 1 : 0,
      taskData.recurrenceType || null,
      taskData.recurrenceInterval || null,
      now,
      now
    );

    const task = await this.getTaskById(id);
    if (!task) throw new Error('Failed to create task');

    return task;
  }

  /**
   * Update an existing task with special handling for recurring tasks
   */
  async updateTask(taskId: string, updates: UpdateTaskRequest): Promise<Task> {
    if (!this.db) throw new Error('Database not initialized');

    return this.runInTransaction(() => {
      const existingTask = this.getTaskByIdSync(taskId);
      if (!existingTask) {
        throw new Error('Task not found');
      }

      // Special handling for completing recurring tasks
      if (updates.isCompleted === true && existingTask.isRecurring && !existingTask.isCompleted) {
        return this.handleRecurringTaskCompletion(existingTask);
      }

      // Regular task update
      const setClause: string[] = [];
      const params: any[] = [];

      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined) {
          if (key === 'isCompleted') {
            setClause.push('isCompleted = ?', 'completedAt = ?');
            params.push(value ? 1 : 0, value ? new Date().toISOString() : null);
          } else if (key === 'isRecurring') {
            setClause.push('isRecurring = ?');
            params.push(value ? 1 : 0);
          } else {
            setClause.push(`${key} = ?`);
            params.push(value);
          }
        }
      });

      if (setClause.length === 0) {
        return existingTask;
      }

      setClause.push('updatedAt = ?');
      params.push(new Date().toISOString());
      params.push(taskId);

      const stmt = this.db!.prepare(`
        UPDATE tasks SET ${setClause.join(', ')} WHERE id = ?
      `);
      stmt.run(...params);

      const updatedTask = this.getTaskByIdSync(taskId);
      if (!updatedTask) throw new Error('Failed to update task');

      return updatedTask;
    });
  }

  /**
   * Handle completion of recurring tasks (creates completed instance, updates original)
   */
  private handleRecurringTaskCompletion(task: Task): Task {
    if (!this.db) throw new Error('Database not initialized');

    const now = new Date().toISOString();
    const completedInstanceId = crypto.randomUUID();

    // Create completed instance (non-recurring)
    const insertCompleted = this.db.prepare(`
      INSERT INTO tasks (
        id, title, description, dueDate, dueTime, isCompleted, completedAt,
        priority, category, isRecurring, recurrenceType, recurrenceInterval,
        createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, 1, ?, ?, ?, 0, NULL, NULL, ?, ?)
    `);

    insertCompleted.run(
      completedInstanceId,
      task.title,
      task.description,
      task.dueDate,
      task.dueTime,
      now,
      task.priority,
      task.category,
      task.createdAt,
      now
    );

    // Calculate next due date for original task
    const nextDueDate = this.calculateNextDueDate(task.dueDate, task.recurrenceType!, task.recurrenceInterval || 1);

    // Update original task with next due date
    const updateOriginal = this.db.prepare(`
      UPDATE tasks SET dueDate = ?, updatedAt = ? WHERE id = ?
    `);
    updateOriginal.run(nextDueDate, now, task.id);

    // Return updated original task
    const updatedTask = this.getTaskByIdSync(task.id);
    if (!updatedTask) throw new Error('Failed to update recurring task');

    return updatedTask;
  }

  /**
   * Calculate next due date for recurring tasks
   */
  private calculateNextDueDate(currentDate: string, recurrenceType: RecurrenceType, interval: number): string {
    const date = new Date(currentDate);

    switch (recurrenceType) {
      case RecurrenceType.Daily:
        date.setDate(date.getDate() + interval);
        break;
      case RecurrenceType.Weekly:
        date.setDate(date.getDate() + (interval * 7));
        break;
      case RecurrenceType.Monthly:
        date.setMonth(date.getMonth() + interval);
        break;
      case RecurrenceType.Yearly:
        date.setFullYear(date.getFullYear() + interval);
        break;
    }

    return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
  }

  /**
   * Delete a task permanently
   */
  async deleteTask(taskId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare('DELETE FROM tasks WHERE id = ?');
    const result = stmt.run(taskId);

    if (result.changes === 0) {
      throw new Error('Task not found');
    }
  }

  /**
   * Get all settings
   */
  async getSettings(): Promise<Settings> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare('SELECT key, value FROM settings');
    const rows = stmt.all() as { key: string; value: string }[];

    const settings: Settings = {
      showCompleted: true,
      seeded: false,
    };

    rows.forEach(row => {
      if (row.key === 'showCompleted' || row.key === 'seeded') {
        settings[row.key] = row.value === 'true';
      } else {
        settings[row.key] = row.value;
      }
    });

    return settings;
  }

  /**
   * Update a specific setting
   */
  async updateSetting(key: string, value: any): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO settings (key, value, updatedAt) 
      VALUES (?, ?, datetime('now'))
    `);
    
    stmt.run(key, typeof value === 'boolean' ? value.toString() : value);
  }

  /**
   * Update user credentials (for authentication service)
   */
  async updateUserCredentials(username: string, passwordHash: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      UPDATE user_credentials 
      SET password_hash = ?, updated_at = datetime('now') 
      WHERE username = ?
    `);
    
    const result = stmt.run(passwordHash, username);
    if (result.changes === 0) {
      throw new Error('User not found');
    }
  }

  /**
   * Get user credentials (for authentication service)
   */
  async getUserCredentials(username: string): Promise<{ id: number; username: string; password_hash: string; login_attempts: number; locked_until: string | null } | null> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      SELECT id, username, password_hash, login_attempts, locked_until 
      FROM user_credentials 
      WHERE username = ?
    `);
    
    return stmt.get(username) as any;
  }

  /**
   * Update login attempts and lockout status
   */
  async updateLoginAttempts(username: string, attempts: number, lockedUntil?: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      UPDATE user_credentials 
      SET login_attempts = ?, locked_until = ?, updated_at = datetime('now')
      WHERE username = ?
    `);
    
    stmt.run(attempts, lockedUntil || null, username);
  }

  /**
   * Record successful login
   */
  async recordSuccessfulLogin(username: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      UPDATE user_credentials 
      SET login_attempts = 0, locked_until = NULL, last_login = datetime('now'), updated_at = datetime('now')
      WHERE username = ?
    `);
    
    stmt.run(username);
  }

  /**
   * Run database maintenance operations
   */
  async runMaintenance(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Run VACUUM to reclaim space
    this.db.exec('VACUUM');
    
    // Run ANALYZE to update statistics
    this.db.exec('ANALYZE');
    
    // Update maintenance timestamps
    const stmt = this.db.prepare(`
      UPDATE database_metadata 
      SET last_vacuum = datetime('now'), last_analyze = datetime('now'), updated_at = datetime('now')
      WHERE id = 1
    `);
    stmt.run();
  }

  /**
   * Check database integrity
   */
  async checkIntegrity(): Promise<boolean> {
    if (!this.db) throw new Error('Database not initialized');

    const result = this.db.prepare('PRAGMA integrity_check').get() as { integrity_check: string };
    const isHealthy = result.integrity_check === 'ok';

    // Update integrity check timestamp
    const stmt = this.db.prepare(`
      UPDATE database_metadata 
      SET last_integrity_check = datetime('now'), updated_at = datetime('now')
      WHERE id = 1
    `);
    stmt.run();

    return isHealthy;
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<{ taskCount: number; completedTaskCount: number; settingsCount: number; size: number }> {
    if (!this.db) throw new Error('Database not initialized');

    const taskCount = this.db.prepare('SELECT COUNT(*) as count FROM tasks').get() as { count: number };
    const completedTaskCount = this.db.prepare('SELECT COUNT(*) as count FROM tasks WHERE isCompleted = 1').get() as { count: number };
    const settingsCount = this.db.prepare('SELECT COUNT(*) as count FROM settings').get() as { count: number };
    
    let size = 0;
    try {
      const stats = fs.statSync(config.database.path);
      size = stats.size;
    } catch (error) {
      // File might not exist yet
    }

    return {
      taskCount: taskCount.count,
      completedTaskCount: completedTaskCount.count,
      settingsCount: settingsCount.count,
      size,
    };
  }

  /**
   * Close database connection
   */
  async close(): Promise<void> {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.isInitialized = false;
    }
  }

  /**
   * Synchronous version of getTaskById for use within transactions
   */
  private getTaskByIdSync(id: string): Task | null {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare('SELECT * FROM tasks WHERE id = ?');
    const row = stmt.get(id) as any;

    return row ? this.rowToTask(row) : null;
  }

  /**
   * Convert database row to Task object
   */
  private rowToTask(row: any): Task {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      dueDate: row.dueDate,
      dueTime: row.dueTime,
      isCompleted: row.isCompleted === 1,
      completedAt: row.completedAt,
      priority: row.priority as Priority,
      category: row.category,
      isRecurring: row.isRecurring === 1,
      recurrenceType: row.recurrenceType as RecurrenceType,
      recurrenceInterval: row.recurrenceInterval,
      createdAt: row.createdAt,
      updatedAt: row.updatedAt,
    };
  }

  /**
   * Get initialization status
   */
  get initialized(): boolean {
    return this.isInitialized;
  }
}

// Export singleton instance
export const dbService = new DatabaseService();
export default dbService;