/**
 * Location: /backend/src/services/authService.ts
 * Purpose: Authentication service with bcrypt password hashing and JWT token management
 * Usage: Handles user authentication, session management, and token operations
 */

import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { config } from '../config';
import { dbService } from './dbService';
import { User, JWTPayload, AuthResult, Session } from '../types';

class AuthService {
  private sessions = new Map<string, Session>();

  /**
   * Initialize authentication service and migrate existing credentials
   */
  async initialize(): Promise<void> {
    try {
      // Hash the existing hardcoded password and update in database
      const existingPassword = 'Lookup88?'; // Current hardcoded password from frontend
      const hashedPassword = await this.hashPassword(existingPassword);
      
      // Check if user exists, if not this is a database initialization issue
      const existingUser = await dbService.getUserCredentials('scharway');
      if (!existingUser) {
        throw new Error('Default user not found in database. Database initialization may have failed.');
      }
      
      await dbService.updateUserCredentials('scharway', hashedPassword);
      
      // Start session cleanup interval
      this.startSessionCleanup();
      
      if (config.nodeEnv === 'development') {
        console.log('Authentication service initialized successfully');
      }
    } catch (error) {
      throw new Error(`Failed to initialize authentication service: ${error}`);
    }
  }

  /**
   * Authenticate user with username and password
   */
  async login(username: string, password: string, req: any): Promise<AuthResult> {
    try {
      // Check if account is locked
      if (await this.isAccountLocked(username)) {
        throw new Error('Account is temporarily locked due to too many failed login attempts');
      }

      // Get user credentials from database
      const userCredentials = await dbService.getUserCredentials(username);
      if (!userCredentials) {
        await this.recordFailedLogin(username);
        throw new Error('Invalid username or password');
      }

      // Verify password
      const isPasswordValid = await this.verifyPassword(password, userCredentials.password_hash);
      if (!isPasswordValid) {
        await this.recordFailedLogin(username);
        throw new Error('Invalid username or password');
      }

      // Password is correct, reset login attempts
      await dbService.recordSuccessfulLogin(username);

      // Generate JWT token
      const tokenId = crypto.randomUUID();
      const user: User = {
        id: userCredentials.id,
        username: userCredentials.username,
      };

      const token = this.generateToken(user, tokenId);
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000).toISOString(); // 5 minutes

      // Create session
      this.createSession(tokenId, user.id, req);

      return {
        token,
        user,
        expiresAt,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Logout user and invalidate session
   */
  async logout(token: string): Promise<void> {
    try {
      const payload = this.validateToken(token);
      this.invalidateSession(payload.jti);
    } catch (error) {
      // Token might be invalid, but still proceed with logout
    }
  }

  /**
   * Validate JWT token and return payload
   */
  validateToken(token: string): JWTPayload {
    try {
      const payload = jwt.verify(token, config.auth.jwtSecret, {
        algorithms: ['HS256'],
      }) as JWTPayload;

      // Check if session is still valid
      if (!this.isSessionValid(payload.jti)) {
        throw new Error('Session is no longer valid');
      }

      // Update session activity
      this.updateSessionActivity(payload.jti);

      return payload;
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error('Invalid token');
      } else if (error instanceof jwt.TokenExpiredError) {
        throw new Error('Token has expired');
      } else {
        throw error;
      }
    }
  }

  /**
   * Refresh JWT token if within refresh window
   */
  async refreshToken(token: string): Promise<{ token: string; expiresAt: string }> {
    try {
      // Validate current token (this will throw if expired)
      const payload = this.validateToken(token);
      
      // Check if token is within refresh window (allow refresh up to 1 minute after expiration)
      const now = Math.floor(Date.now() / 1000);
      const refreshWindow = 60; // 1 minute
      
      if (now > payload.exp + refreshWindow) {
        throw new Error('Token is too old to refresh');
      }

      // Generate new token with same user info but new expiration
      const user: User = {
        id: parseInt(payload.sub),
        username: payload.username,
      };

      const newTokenId = crypto.randomUUID();
      const newToken = this.generateToken(user, newTokenId);
      const expiresAt = new Date(Date.now() + 5 * 60 * 1000).toISOString();

      // Update session with new token ID
      this.updateSessionTokenId(payload.jti, newTokenId);

      return {
        token: newToken,
        expiresAt,
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Get user information from token
   */
  async getUserFromToken(token: string): Promise<User> {
    const payload = this.validateToken(token);
    return {
      id: parseInt(payload.sub),
      username: payload.username,
    };
  }

  /**
   * Hash password using bcrypt
   */
  async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, config.auth.bcryptSaltRounds);
  }

  /**
   * Verify password against hash
   */
  async verifyPassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  /**
   * Generate JWT token
   */
  private generateToken(user: User, tokenId: string): string {
    const payload: JWTPayload = {
      sub: user.id.toString(),
      username: user.username,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (5 * 60), // 5 minutes
      jti: tokenId,
    };

    return jwt.sign(payload, config.auth.jwtSecret, {
      algorithm: 'HS256',
    });
  }

  /**
   * Create new session
   */
  private createSession(tokenId: string, userId: number, req: any): void {
    const session: Session = {
      tokenId,
      userId,
      createdAt: new Date(),
      lastActivity: new Date(),
      ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
      userAgent: req.get('User-Agent') || 'unknown',
      isActive: true,
    };

    this.sessions.set(tokenId, session);
  }

  /**
   * Check if session is valid
   */
  private isSessionValid(tokenId: string): boolean {
    const session = this.sessions.get(tokenId);
    return session?.isActive === true;
  }

  /**
   * Update session activity timestamp
   */
  private updateSessionActivity(tokenId: string): void {
    const session = this.sessions.get(tokenId);
    if (session && session.isActive) {
      session.lastActivity = new Date();
    }
  }

  /**
   * Update session token ID (for token refresh)
   */
  private updateSessionTokenId(oldTokenId: string, newTokenId: string): void {
    const session = this.sessions.get(oldTokenId);
    if (session) {
      this.sessions.delete(oldTokenId);
      session.tokenId = newTokenId;
      session.lastActivity = new Date();
      this.sessions.set(newTokenId, session);
    }
  }

  /**
   * Invalidate session
   */
  private invalidateSession(tokenId: string): void {
    const session = this.sessions.get(tokenId);
    if (session) {
      session.isActive = false;
      this.sessions.delete(tokenId);
    }
  }

  /**
   * Check if account is locked due to failed login attempts
   */
  private async isAccountLocked(username: string): Promise<boolean> {
    const userCredentials = await dbService.getUserCredentials(username);
    if (!userCredentials) return false;

    // Check if account is currently locked
    if (userCredentials.locked_until) {
      const lockedUntil = new Date(userCredentials.locked_until);
      const now = new Date();
      
      if (lockedUntil > now) {
        return true; // Account is still locked
      } else {
        // Lockout period has expired, reset attempts
        await dbService.updateLoginAttempts(username, 0);
        return false;
      }
    }

    return false;
  }

  /**
   * Record failed login attempt and lock account if necessary
   */
  private async recordFailedLogin(username: string): Promise<void> {
    const userCredentials = await dbService.getUserCredentials(username);
    if (!userCredentials) return;

    const newAttempts = userCredentials.login_attempts + 1;
    let lockedUntil: string | undefined;

    // Lock account after 5 failed attempts for 30 minutes
    if (newAttempts >= 5) {
      const lockoutTime = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
      lockedUntil = lockoutTime.toISOString();
    }

    await dbService.updateLoginAttempts(username, newAttempts, lockedUntil);
  }

  /**
   * Start automatic session cleanup
   */
  private startSessionCleanup(): void {
    // Clean up expired sessions every minute
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60 * 1000);
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = new Date();
    const sessionTimeout = config.auth.sessionTimeout;

    for (const [tokenId, session] of this.sessions) {
      const timeSinceLastActivity = now.getTime() - session.lastActivity.getTime();
      
      if (timeSinceLastActivity > sessionTimeout) {
        this.invalidateSession(tokenId);
      }
    }
  }

  /**
   * Get session information for a token
   */
  getSessionInfo(tokenId: string): Session | null {
    return this.sessions.get(tokenId) || null;
  }

  /**
   * Get all active sessions (for debugging/monitoring)
   */
  getActiveSessions(): Session[] {
    return Array.from(this.sessions.values()).filter(session => session.isActive);
  }

  /**
   * Invalidate all sessions (for security purposes)
   */
  invalidateAllSessions(): void {
    for (const [tokenId] of this.sessions) {
      this.invalidateSession(tokenId);
    }
  }
}

// Export singleton instance
export const authService = new AuthService();
export default authService;