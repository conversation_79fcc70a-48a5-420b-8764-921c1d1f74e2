/**
 * Location: /backend/src/types/index.ts
 * Purpose: TypeScript type definitions for LifeTracker backend API
 * Usage: Shared types used throughout the backend application for type safety
 */

import { Request } from 'express';

// Priority levels for tasks
export enum Priority {
  Low = 'Low',
  Medium = 'Medium',
  High = 'High'
}

// Recurrence types for recurring tasks
export enum RecurrenceType {
  Daily = 'Daily',
  Weekly = 'Weekly',
  Monthly = 'Monthly',
  Yearly = 'Yearly'
}

// Core Task interface - matches frontend Task interface exactly
export interface Task {
  id: string;
  title: string;
  description?: string;
  dueDate: string; // YYYY-MM-DD format
  dueTime: string; // HH:mm format
  isCompleted: boolean;
  completedAt?: string; // ISO timestamp
  priority: Priority;
  category?: string;
  isRecurring: boolean;
  recurrenceType?: RecurrenceType;
  recurrenceInterval?: number;
  createdAt: string; // ISO timestamp
  updatedAt: string; // ISO timestamp
}

// User interface for authentication
export interface User {
  id: number;
  username: string;
}

// Settings interface
export interface Settings {
  showCompleted: boolean;
  seeded: boolean;
  [key: string]: any; // Allow additional settings
}

// JWT payload interface
export interface JWTPayload {
  sub: string; // User ID
  username: string;
  iat: number; // Issued at
  exp: number; // Expiration
  jti: string; // Token ID for revocation
}

// Session interface for session management
export interface Session {
  tokenId: string;
  userId: number;
  createdAt: Date;
  lastActivity: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
}

// API Request/Response interfaces

// Standard API response wrapper
export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    field?: string;
    details?: any;
  };
  metadata: {
    timestamp: string;
    requestId: string;
    pagination?: {
      page: number;
      limit: number;
      total: number;
      hasNext: boolean;
    };
  };
}

// Authentication requests
export interface LoginRequest {
  username: string;
  password: string;
}

export interface AuthResult {
  token: string;
  user: User;
  expiresAt: string;
}

// Task requests
export interface CreateTaskRequest {
  title: string;
  description?: string;
  dueDate: string;
  dueTime: string;
  priority: Priority;
  category?: string;
  isRecurring?: boolean;
  recurrenceType?: RecurrenceType;
  recurrenceInterval?: number;
}

export interface UpdateTaskRequest {
  title?: string;
  description?: string;
  dueDate?: string;
  dueTime?: string;
  isCompleted?: boolean;
  priority?: Priority;
  category?: string;
  isRecurring?: boolean;
  recurrenceType?: RecurrenceType;
  recurrenceInterval?: number;
}

export interface TaskFilters {
  completed?: boolean;
  category?: string;
  priority?: Priority;
  dueDate?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

// Settings requests
export interface UpdateSettingsRequest {
  showCompleted?: boolean;
  seeded?: boolean;
  [key: string]: any;
}

export interface UpdateSettingRequest {
  value: boolean | string | number;
}

// Health check interfaces
export interface DatabaseHealth {
  connected: boolean;
  lastCheck: string;
  responseTime: number;
  version?: string;
  size?: number;
  taskCount?: number;
  settingsCount?: number;
  lastMaintenance?: string;
  integrity?: boolean;
}

export interface MemoryInfo {
  used: number;
  free: number;
  percentage: number;
}

export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  uptime: number;
  database: DatabaseHealth;
  memory: MemoryInfo;
}

// Configuration interfaces
export interface DatabaseConfig {
  path: string;
  backupPath: string;
  vacuumInterval: number;
  analyzeInterval: number;
}

export interface AuthConfig {
  jwtSecret: string;
  jwtExpiry: string;
  bcryptSaltRounds: number;
  sessionTimeout: number;
}

export interface SecurityConfig {
  rateLimitWindow: number;
  rateLimitMax: number;
  authRateLimitMax: number;
  corsOrigins: string[];
}

export interface AppConfig {
  port: number;
  nodeEnv: string;
  database: DatabaseConfig;
  auth: AuthConfig;
  security: SecurityConfig;
}

// Express request extension for authenticated requests
export interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    username: string;
    tokenId: string;
  };
}

// Error types
export interface AppError extends Error {
  statusCode: number;
  code: string;
  field?: string;
  details?: any;
}