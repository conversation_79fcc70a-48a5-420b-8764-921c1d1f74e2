/**
 * Location: /backend/src/app.ts
 * Purpose: Main Express application setup with all middleware and route configurations
 * Usage: Entry point for the LifeTracker backend server with comprehensive security and functionality
 */

import express, { Application, Request, Response } from 'express';
import { config } from './config';
import { dbService } from './services/dbService';
import { authService } from './services/authService';
import { maintenanceService } from './services/maintenanceService';

// Middleware imports
import { 
  helmetConfig, 
  corsConfig, 
  generalRateLimit, 
  requestId, 
  additionalSecurityHeaders,
  requestSizeLimit,
  validateContentType,
  noCache,
  requestLogger 
} from './middleware/security';
import { errorHandler, notFoundHandler } from './middleware/errorHandler';

// Route imports
import authRoutes from './routes/auth';
import taskRoutes from './routes/tasks';
import settingsRoutes from './routes/settings';
import healthRoutes from './routes/health';

class LifeTrackerApp {
  public app: Application;
  private server: any;

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Setup middleware stack in correct order
   */
  private setupMiddleware(): void {
    // Request tracking and logging
    this.app.use(requestId);
    if (config.nodeEnv === 'development') {
      this.app.use(requestLogger);
    }

    // Security middleware
    this.app.use(helmetConfig);
    this.app.use(corsConfig);
    this.app.use(additionalSecurityHeaders);
    
    // Rate limiting (applied globally, specific limits in routes)
    this.app.use('/api', generalRateLimit);
    
    // Request parsing and validation
    this.app.use(requestSizeLimit);
    this.app.use(express.json({ limit: '1mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '1mb' }));
    this.app.use(validateContentType);

    // Disable caching for API endpoints
    this.app.use('/api', noCache);

    // Health check (no auth required)
    this.app.get('/health', (req: Request, res: Response) => {
      res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      });
    });
  }

  /**
   * Setup API routes
   */
  private setupRoutes(): void {
    // API routes with versioning
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/tasks', taskRoutes);
    this.app.use('/api/settings', settingsRoutes);
    this.app.use('/api/health', healthRoutes);

    // API info endpoint
    this.app.get('/api', (req: Request, res: Response) => {
      res.json({
        name: 'LifeTracker API',
        version: '1.0.0',
        description: 'Personal task management backend service',
        documentation: '/api/docs',
        health: '/api/health',
        endpoints: {
          authentication: '/api/auth',
          tasks: '/api/tasks',
          settings: '/api/settings',
          health: '/api/health',
        },
        timestamp: new Date().toISOString(),
      });
    });

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        message: 'LifeTracker Backend API',
        version: '1.0.0',
        status: 'operational',
        api: '/api',
        health: '/health',
        timestamp: new Date().toISOString(),
      });
    });
  }

  /**
   * Setup error handling middleware (must be last)
   */
  private setupErrorHandling(): void {
    // 404 handler for unknown routes
    this.app.use(notFoundHandler);
    
    // Global error handler
    this.app.use(errorHandler);
  }

  /**
   * Initialize all services
   */
  public async initialize(): Promise<void> {
    try {
      console.log('Initializing LifeTracker Backend...');

      // Initialize database service
      console.log('Initializing database service...');
      await dbService.initialize();
      console.log('Database service initialized successfully');

      // Initialize authentication service
      console.log('Initializing authentication service...');
      await authService.initialize();
      console.log('Authentication service initialized successfully');

      // Initialize maintenance service
      console.log('Initializing maintenance service...');
      await maintenanceService.initialize();
      console.log('Maintenance service initialized successfully');

      console.log('All services initialized successfully');
    } catch (error) {
      console.error('Failed to initialize services:', error);
      throw error;
    }
  }

  /**
   * Start the server
   */
  public async start(): Promise<void> {
    try {
      // Initialize services first
      await this.initialize();

      // Start HTTP server
      this.server = this.app.listen(config.port, () => {
        console.log(`LifeTracker Backend running on port ${config.port}`);
        console.log(`Environment: ${config.nodeEnv}`);
        console.log(`Health check: http://localhost:${config.port}/health`);
        console.log(`API documentation: http://localhost:${config.port}/api`);
        
        if (config.nodeEnv === 'development') {
          console.log(`Development server ready for requests`);
        }
      });

      // Handle server errors
      this.server.on('error', (error: any) => {
        console.error('Server error:', error);
        if (error.code === 'EADDRINUSE') {
          console.error(`Port ${config.port} is already in use`);
        }
        process.exit(1);
      });

      // Graceful shutdown handling
      this.setupGracefulShutdown();

    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  /**
   * Setup graceful shutdown handlers
   */
  private setupGracefulShutdown(): void {
    const shutdown = async (signal: string) => {
      console.log(`\nReceived ${signal}. Starting graceful shutdown...`);

      try {
        // Close HTTP server
        if (this.server) {
          await new Promise<void>((resolve) => {
            this.server.close(() => {
              console.log('HTTP server closed');
              resolve();
            });
          });
        }

        // Stop maintenance service
        maintenanceService.stop();
        console.log('Maintenance service stopped');

        // Close database connection
        await dbService.close();
        console.log('Database connection closed');

        console.log('Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        console.error('Error during shutdown:', error);
        process.exit(1);
      }
    };

    // Handle various shutdown signals
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGQUIT', () => shutdown('SIGQUIT'));

    // Handle uncaught exceptions and unhandled rejections
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      shutdown('UNCAUGHT_EXCEPTION');
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      shutdown('UNHANDLED_REJECTION');
    });
  }

  /**
   * Stop the server
   */
  public async stop(): Promise<void> {
    if (this.server) {
      await new Promise<void>((resolve) => {
        this.server.close(resolve);
      });
    }

    // Stop services
    maintenanceService.stop();
    await dbService.close();
  }
}

// Create and export app instance
const lifeTrackerApp = new LifeTrackerApp();

// Start server if this file is run directly
if (require.main === module) {
  lifeTrackerApp.start().catch((error) => {
    console.error('Failed to start LifeTracker Backend:', error);
    process.exit(1);
  });
}

export default lifeTrackerApp;
export { LifeTrackerApp };