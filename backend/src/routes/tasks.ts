/**
 * Location: /backend/src/routes/tasks.ts
 * Purpose: Task management routes with CRUD operations and recurring task logic
 * Usage: Handles all task-related API endpoints with full functionality preservation
 */

import { Router, Response } from 'express';
import { dbService } from '../services/dbService';
import { authenticateToken } from '../middleware/auth';
import { 
  validateCreateTask, 
  validateUpdateTask, 
  validateTaskQuery, 
  validateTaskId 
} from '../middleware/validation';
import { Priority, RecurrenceType } from '../types';
import { asyncHandler, successResponse, errors } from '../middleware/errorHandler';
import { AuthenticatedRequest, CreateTaskRequest, UpdateTaskRequest, TaskFilters } from '../types';

const router = Router();

// All task routes require authentication
router.use(authenticateToken);

/**
 * GET /api/tasks
 * Retrieve all tasks with optional filtering
 */
router.get('/',
  validateTaskQuery, // Validate query parameters
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    
    // Extract and sanitize query parameters
    const filters: TaskFilters = {
      completed: req.query.completed === 'true' ? true : req.query.completed === 'false' ? false : undefined,
      category: req.query.category as string,
      priority: req.query.priority as any,
      dueDate: req.query.dueDate as string,
      search: req.query.search as string,
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
    };

    // Remove undefined values
    Object.keys(filters).forEach(key => {
      if (filters[key as keyof TaskFilters] === undefined) {
        delete filters[key as keyof TaskFilters];
      }
    });

    const tasks = await dbService.getTasks(filters);
    
    // Calculate pagination metadata if limit was provided
    let pagination;
    if (filters.limit) {
      const total = tasks.length;
      const page = Math.floor((filters.offset || 0) / filters.limit) + 1;
      const hasNext = total === filters.limit; // Simple check - more accurate would require total count query
      
      pagination = {
        page,
        limit: filters.limit,
        total,
        hasNext,
      };
    }

    successResponse(res, tasks, 200, requestId, pagination);
  })
);

/**
 * GET /api/tasks/:id
 * Retrieve a specific task by ID
 */
router.get('/:id',
  validateTaskId, // Validate task ID format
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const { id } = req.params;

    const task = await dbService.getTaskById(id);
    
    if (!task) {
      throw errors.notFound('Task');
    }

    successResponse(res, task, 200, requestId);
  })
);

/**
 * POST /api/tasks
 * Create a new task
 */
router.post('/',
  validateCreateTask, // Validate request body
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const taskData = req.body as CreateTaskRequest;

    // Validate recurring task configuration
    if (taskData.isRecurring && !taskData.recurrenceType) {
      throw errors.badRequest('Recurrence type is required for recurring tasks', 'recurrenceType');
    }

    if (taskData.isRecurring && taskData.recurrenceInterval && taskData.recurrenceInterval < 1) {
      throw errors.badRequest('Recurrence interval must be at least 1', 'recurrenceInterval');
    }

    try {
      const task = await dbService.addTask(taskData);
      successResponse(res, task, 201, requestId);
    } catch (error: any) {
      if (error.message.includes('constraint')) {
        throw errors.badRequest('Invalid task data - constraint violation');
      }
      throw errors.internal('Failed to create task');
    }
  })
);

/**
 * PUT /api/tasks/:id
 * Update an existing task (includes special handling for recurring tasks)
 */
router.put('/:id',
  validateTaskId, // Validate task ID format
  validateUpdateTask, // Validate request body
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const { id } = req.params;
    const updates = req.body as UpdateTaskRequest;

    // Check if task exists first
    const existingTask = await dbService.getTaskById(id);
    if (!existingTask) {
      throw errors.notFound('Task');
    }

    // Validate recurring task updates
    if (updates.isRecurring === true && !updates.recurrenceType && !existingTask.recurrenceType) {
      throw errors.badRequest('Recurrence type is required when making task recurring', 'recurrenceType');
    }

    try {
      const updatedTask = await dbService.updateTask(id, updates);
      successResponse(res, updatedTask, 200, requestId);
    } catch (error: any) {
      if (error.message.includes('Task not found')) {
        throw errors.notFound('Task');
      } else if (error.message.includes('constraint')) {
        throw errors.badRequest('Invalid task data - constraint violation');
      }
      throw errors.internal('Failed to update task');
    }
  })
);

/**
 * DELETE /api/tasks/:id
 * Permanently delete a task
 */
router.delete('/:id',
  validateTaskId, // Validate task ID format
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const { id } = req.params;

    try {
      await dbService.deleteTask(id);
      
      successResponse(res, {
        message: 'Task deleted successfully',
        deletedTaskId: id,
      }, 200, requestId);
    } catch (error: any) {
      if (error.message.includes('Task not found')) {
        throw errors.notFound('Task');
      }
      throw errors.internal('Failed to delete task');
    }
  })
);

/**
 * POST /api/tasks/seed
 * Seed initial tasks for new users (internal use)
 */
router.post('/seed',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    
    // Check if tasks have already been seeded
    const settings = await dbService.getSettings();
    if (settings.seeded) {
      throw errors.badRequest('Tasks have already been seeded for this user');
    }

    // Define initial seed tasks with current dates
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const seedTasks: CreateTaskRequest[] = [
      {
        title: "Welcome to LifeTracker!",
        description: "This is your first task. You can edit or delete it anytime.",
        dueDate: today.toISOString().split('T')[0],
        dueTime: "09:00",
        priority: Priority.Medium,
        category: "Personal",
        isRecurring: false,
      },
      {
        title: "Plan Weekly Goals",
        description: "Set your goals for the upcoming week",
        dueDate: tomorrow.toISOString().split('T')[0],
        dueTime: "10:00",
        priority: Priority.High,
        category: "Planning",
        isRecurring: true,
        recurrenceType: RecurrenceType.Weekly,
        recurrenceInterval: 1,
      },
      {
        title: "Review Completed Tasks",
        description: "Take a moment to review what you've accomplished",
        dueDate: nextWeek.toISOString().split('T')[0],
        dueTime: "17:00",
        priority: Priority.Low,
        category: "Review",
        isRecurring: false,
      },
    ];

    try {
      const seededTasks = [];
      
      // Create each seed task
      for (const taskData of seedTasks) {
        const task = await dbService.addTask(taskData);
        seededTasks.push(task);
      }

      // Mark as seeded
      await dbService.updateSetting('seeded', true);

      successResponse(res, {
        seededTasks,
        count: seededTasks.length,
      }, 201, requestId);
    } catch (error: any) {
      throw errors.internal('Failed to seed tasks');
    }
  })
);

/**
 * GET /api/tasks/stats
 * Get task statistics
 */
router.get('/stats',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    try {
      const allTasks = await dbService.getTasks();
      const completedTasks = await dbService.getTasks({ completed: true });
      const pendingTasks = await dbService.getTasks({ completed: false });
      const recurringTasks = allTasks.filter(task => task.isRecurring);
      
      // Calculate overdue tasks
      const today = new Date().toISOString().split('T')[0];
      const overdueTasks = pendingTasks.filter(task => task.dueDate < today);
      
      // Calculate tasks by priority
      const highPriorityTasks = pendingTasks.filter(task => task.priority === 'High');
      const mediumPriorityTasks = pendingTasks.filter(task => task.priority === 'Medium');
      const lowPriorityTasks = pendingTasks.filter(task => task.priority === 'Low');
      
      // Calculate tasks by category
      const categories = [...new Set(allTasks.map(task => task.category).filter(Boolean))];
      const tasksByCategory = categories.map(category => ({
        category,
        count: allTasks.filter(task => task.category === category).length,
      }));

      const stats = {
        total: allTasks.length,
        completed: completedTasks.length,
        pending: pendingTasks.length,
        overdue: overdueTasks.length,
        recurring: recurringTasks.length,
        byPriority: {
          high: highPriorityTasks.length,
          medium: mediumPriorityTasks.length,
          low: lowPriorityTasks.length,
        },
        byCategory: tasksByCategory,
        completionRate: allTasks.length > 0 ? Math.round((completedTasks.length / allTasks.length) * 100) : 0,
      };

      successResponse(res, stats, 200, requestId);
    } catch (error: any) {
      throw errors.internal('Failed to get task statistics');
    }
  })
);

export default router;