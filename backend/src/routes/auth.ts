/**
 * Location: /backend/src/routes/auth.ts
 * Purpose: Authentication routes for login, logout, session management, and token refresh
 * Usage: Handles all authentication-related API endpoints with comprehensive security
 */

import { Router, Request, Response } from 'express';
import { authService } from '../services/authService';
import { authenticateToken } from '../middleware/auth';
import { validateLogin } from '../middleware/validation';
import { authRateLimit } from '../middleware/security';
import { asyncHandler, successResponse, errors } from '../middleware/errorHandler';
import { AuthenticatedRequest, LoginRequest } from '../types';

const router = Router();

/**
 * POST /api/auth/login
 * Authenticate user credentials and return JWT token
 */
router.post('/login', 
  authRateLimit, // Apply rate limiting
  validateLogin, // Validate request body
  asyncHandler(async (req: Request, res: Response) => {
    const { username, password } = req.body as LoginRequest;
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    try {
      const authResult = await authService.login(username, password, req);
      
      successResponse(res, authResult, 200, requestId);
    } catch (error: any) {
      // Authentication errors should be handled specifically
      if (error.message.includes('Invalid username or password')) {
        throw errors.unauthorized('Invalid username or password');
      } else if (error.message.includes('locked')) {
        throw errors.forbidden('Account is temporarily locked due to too many failed login attempts');
      } else {
        throw errors.internal('Authentication failed');
      }
    }
  })
);

/**
 * POST /api/auth/logout
 * Invalidate current JWT token and session
 */
router.post('/logout',
  authenticateToken, // Require authentication
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7);
      await authService.logout(token);
    }

    successResponse(res, { message: 'Successfully logged out' }, 200, requestId);
  })
);

/**
 * GET /api/auth/session
 * Validate current session and return user information
 */
router.get('/session',
  authenticateToken, // Require authentication
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw errors.unauthorized('No authentication token provided');
    }

    const token = authHeader.substring(7);
    
    try {
      const user = await authService.getUserFromToken(token);
      const payload = authService.validateToken(token);
      
      const sessionData = {
        user,
        expiresAt: new Date(payload.exp * 1000).toISOString(),
        isValid: true,
      };

      successResponse(res, sessionData, 200, requestId);
    } catch (error: any) {
      if (error.message.includes('expired')) {
        throw errors.unauthorized('Authentication token has expired');
      } else if (error.message.includes('Session')) {
        throw errors.unauthorized('Session is no longer valid');
      } else {
        throw errors.unauthorized('Invalid authentication token');
      }
    }
  })
);

/**
 * POST /api/auth/refresh
 * Refresh JWT token if within refresh window
 */
router.post('/refresh',
  authenticateToken, // Require authentication (will validate current token)
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw errors.unauthorized('No authentication token provided');
    }

    const token = authHeader.substring(7);
    
    try {
      const refreshResult = await authService.refreshToken(token);
      
      successResponse(res, refreshResult, 200, requestId);
    } catch (error: any) {
      if (error.message.includes('expired') || error.message.includes('too old')) {
        throw errors.forbidden('Token cannot be refreshed');
      } else if (error.message.includes('Session')) {
        throw errors.unauthorized('Session is no longer valid');
      } else {
        throw errors.unauthorized('Token refresh failed');
      }
    }
  })
);

/**
 * GET /api/auth/me
 * Get current user information (alias for session endpoint)
 */
router.get('/me',
  authenticateToken, // Require authentication
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    
    if (!req.user) {
      throw errors.unauthorized('User information not available');
    }

    const userData = {
      id: req.user.id,
      username: req.user.username,
    };

    successResponse(res, userData, 200, requestId);
  })
);

export default router;