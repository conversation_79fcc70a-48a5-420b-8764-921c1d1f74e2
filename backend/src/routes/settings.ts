/**
 * Location: /backend/src/routes/settings.ts
 * Purpose: Settings management routes for user preferences
 * Usage: Handles all settings-related API endpoints for application configuration
 */

import { Router, Response } from 'express';
import { dbService } from '../services/dbService';
import { authenticateToken } from '../middleware/auth';
import { 
  validateUpdateSettings, 
  validateUpdateSetting, 
  validateSettingKey 
} from '../middleware/validation';
import { asyncHandler, successResponse, errors } from '../middleware/errorHandler';
import { AuthenticatedRequest, UpdateSettingsRequest, UpdateSettingRequest } from '../types';

const router = Router();

// All settings routes require authentication
router.use(authenticateToken);

/**
 * GET /api/settings
 * Retrieve all user settings
 */
router.get('/',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    try {
      const settings = await dbService.getSettings();
      successResponse(res, settings, 200, requestId);
    } catch (error: any) {
      throw errors.internal('Failed to retrieve settings');
    }
  })
);

/**
 * PUT /api/settings
 * Update multiple settings (bulk update)
 */
router.put('/',
  validateUpdateSettings, // Validate request body
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const settingsUpdates = req.body as UpdateSettingsRequest;

    try {
      // Update each setting individually
      for (const [key, value] of Object.entries(settingsUpdates)) {
        if (value !== undefined) {
          await dbService.updateSetting(key, value);
        }
      }

      // Return updated settings
      const updatedSettings = await dbService.getSettings();
      successResponse(res, updatedSettings, 200, requestId);
    } catch (error: any) {
      throw errors.internal('Failed to update settings');
    }
  })
);

/**
 * GET /api/settings/:key
 * Retrieve a specific setting by key
 */
router.get('/:key',
  validateSettingKey, // Validate setting key
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const { key } = req.params;

    try {
      const settings = await dbService.getSettings();
      
      if (!(key in settings)) {
        throw errors.notFound(`Setting '${key}'`);
      }

      const settingData = {
        key,
        value: settings[key],
        updatedAt: new Date().toISOString(), // This could be enhanced to store actual update time
      };

      successResponse(res, settingData, 200, requestId);
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw error;
      }
      throw errors.internal('Failed to retrieve setting');
    }
  })
);

/**
 * PUT /api/settings/:key
 * Update a specific setting
 */
router.put('/:key',
  validateSettingKey, // Validate setting key
  validateUpdateSetting, // Validate request body
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const { key } = req.params;
    const { value } = req.body as UpdateSettingRequest;

    try {
      // Validate setting-specific constraints
      if (key === 'showCompleted' && typeof value !== 'boolean') {
        throw errors.badRequest('showCompleted must be a boolean value', 'value');
      }

      if (key === 'seeded' && typeof value !== 'boolean') {
        throw errors.badRequest('seeded must be a boolean value', 'value');
      }

      // Additional validation for future settings
      if (key === 'theme' && typeof value === 'string') {
        const validThemes = ['light', 'dark', 'auto'];
        if (!validThemes.includes(value)) {
          throw errors.badRequest(`Theme must be one of: ${validThemes.join(', ')}`, 'value');
        }
      }

      if (key === 'language' && typeof value === 'string') {
        const validLanguages = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko'];
        if (!validLanguages.includes(value)) {
          throw errors.badRequest(`Language must be one of: ${validLanguages.join(', ')}`, 'value');
        }
      }

      await dbService.updateSetting(key, value);

      const settingData = {
        key,
        value,
        updatedAt: new Date().toISOString(),
      };

      successResponse(res, settingData, 200, requestId);
    } catch (error: any) {
      if (error.statusCode && error.statusCode < 500) {
        throw error;
      }
      throw errors.internal('Failed to update setting');
    }
  })
);

/**
 * DELETE /api/settings/:key
 * Reset a setting to its default value
 */
router.delete('/:key',
  validateSettingKey, // Validate setting key
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const { key } = req.params;

    try {
      // Define default values for each setting
      const defaults: Record<string, any> = {
        showCompleted: true,
        seeded: false,
        theme: 'light',
        language: 'en',
        timezone: 'America/Chicago',
      };

      if (!(key in defaults)) {
        throw errors.badRequest(`Cannot reset setting '${key}' - no default value defined`);
      }

      const defaultValue = defaults[key];
      await dbService.updateSetting(key, defaultValue);

      const settingData = {
        key,
        value: defaultValue,
        updatedAt: new Date().toISOString(),
        reset: true,
      };

      successResponse(res, settingData, 200, requestId);
    } catch (error: any) {
      if (error.statusCode && error.statusCode < 500) {
        throw error;
      }
      throw errors.internal('Failed to reset setting');
    }
  })
);

/**
 * POST /api/settings/export
 * Export all settings as JSON
 */
router.post('/export',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    try {
      const settings = await dbService.getSettings();
      
      const exportData = {
        settings,
        exportedAt: new Date().toISOString(),
        version: '1.0.0',
      };

      // Set appropriate headers for file download
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="lifetracker-settings-${new Date().toISOString().split('T')[0]}.json"`);

      successResponse(res, exportData, 200, requestId);
    } catch (error: any) {
      throw errors.internal('Failed to export settings');
    }
  })
);

/**
 * POST /api/settings/import
 * Import settings from JSON (with validation)
 */
router.post('/import',
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const importData = req.body;

    try {
      // Validate import data structure
      if (!importData || typeof importData !== 'object') {
        throw errors.badRequest('Invalid import data format');
      }

      if (!importData.settings || typeof importData.settings !== 'object') {
        throw errors.badRequest('Import data must contain a settings object');
      }

      const { settings } = importData;
      const validKeys = ['showCompleted', 'seeded', 'theme', 'language', 'timezone'];
      const importedSettings: Record<string, any> = {};

      // Validate and filter settings
      for (const [key, value] of Object.entries(settings)) {
        if (validKeys.includes(key)) {
          // Type validation
          if ((key === 'showCompleted' || key === 'seeded') && typeof value !== 'boolean') {
            throw errors.badRequest(`Setting '${key}' must be a boolean value`);
          }
          if ((key === 'theme' || key === 'language' || key === 'timezone') && typeof value !== 'string') {
            throw errors.badRequest(`Setting '${key}' must be a string value`);
          }

          importedSettings[key] = value;
        }
      }

      if (Object.keys(importedSettings).length === 0) {
        throw errors.badRequest('No valid settings found in import data');
      }

      // Import settings
      for (const [key, value] of Object.entries(importedSettings)) {
        await dbService.updateSetting(key, value);
      }

      // Return updated settings
      const updatedSettings = await dbService.getSettings();
      
      const result = {
        imported: importedSettings,
        skipped: Object.keys(settings).filter(key => !validKeys.includes(key)),
        current: updatedSettings,
        importedAt: new Date().toISOString(),
      };

      successResponse(res, result, 200, requestId);
    } catch (error: any) {
      if (error.statusCode && error.statusCode < 500) {
        throw error;
      }
      throw errors.internal('Failed to import settings');
    }
  })
);

export default router;