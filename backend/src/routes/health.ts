/**
 * Location: /backend/src/routes/health.ts
 * Purpose: Health check and system monitoring endpoints
 * Usage: Provides system status, database health, and performance metrics
 */

import { Router, Request, Response } from 'express';
import { dbService } from '../services/dbService';
import { maintenanceService } from '../services/maintenanceService';
import { authService } from '../services/authService';
import { optionalAuth } from '../middleware/auth';
import { asyncHandler, successResponse, errors } from '../middleware/errorHandler';
import { AuthenticatedRequest, HealthCheckResponse, DatabaseHealth, MemoryInfo } from '../types';

const router = Router();

/**
 * GET /api/health
 * Basic health check endpoint (public)
 */
router.get('/',
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const startTime = Date.now();

    try {
      // Check database connectivity
      const dbStats = await dbService.getStats();
      const dbResponseTime = Date.now() - startTime;

      // Get memory usage
      const memoryUsage = process.memoryUsage();
      const memory: MemoryInfo = {
        used: memoryUsage.heapUsed,
        free: memoryUsage.heapTotal - memoryUsage.heapUsed,
        percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
      };

      // Basic database health check
      const databaseHealth: DatabaseHealth = {
        connected: dbService.initialized,
        lastCheck: new Date().toISOString(),
        responseTime: dbResponseTime,
        taskCount: dbStats.taskCount,
        settingsCount: dbStats.settingsCount,
        size: dbStats.size,
      };

      const healthData: HealthCheckResponse = {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: databaseHealth,
        memory,
      };

      successResponse(res, healthData, 200, requestId);
    } catch (error: any) {
      const healthData: HealthCheckResponse = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        database: {
          connected: false,
          lastCheck: new Date().toISOString(),
          responseTime: Date.now() - startTime,
        },
        memory: {
          used: 0,
          free: 0,
          percentage: 0,
        },
      };

      res.status(503).json({
        success: false,
        data: healthData,
        error: {
          code: 'SERVICE_UNHEALTHY',
          message: 'Service health check failed',
          details: error.message,
        },
        metadata: {
          timestamp: new Date().toISOString(),
          requestId,
        },
      });
    }
  })
);

/**
 * GET /api/health/database
 * Detailed database health information (requires auth)
 */
router.get('/database',
  optionalAuth, // Optional authentication for more detailed info
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';
    const startTime = Date.now();

    try {
      const dbStats = await dbService.getStats();
      const maintenanceStats = await maintenanceService.getDatabaseStats();
      const dbResponseTime = Date.now() - startTime;

      const databaseHealth: DatabaseHealth = {
        connected: dbService.initialized,
        lastCheck: new Date().toISOString(),
        responseTime: dbResponseTime,
        version: '3.x', // SQLite version would be retrieved from pragma if needed
        size: dbStats.size,
        taskCount: dbStats.taskCount,
        settingsCount: dbStats.settingsCount,
        lastMaintenance: maintenanceStats.backupInfo.lastBackup || undefined,
        integrity: maintenanceStats.health.isHealthy,
      };

      // Include more detailed info if authenticated
      if (req.user) {
        const detailedInfo = {
          ...databaseHealth,
          maintenanceStatus: maintenanceService.getStatus(),
          backupInfo: maintenanceStats.backupInfo,
          integrityMessage: maintenanceStats.health.message,
        };

        successResponse(res, detailedInfo, 200, requestId);
      } else {
        successResponse(res, databaseHealth, 200, requestId);
      }
    } catch (error: any) {
      throw errors.serviceUnavailable('Database health check failed');
    }
  })
);

/**
 * GET /api/health/system
 * System resource information (requires auth)
 */
router.get('/system',
  optionalAuth, // Optional authentication
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    try {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();

      const systemInfo = {
        uptime: process.uptime(),
        memory: {
          heap: {
            used: memoryUsage.heapUsed,
            total: memoryUsage.heapTotal,
            percentage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
          },
          rss: memoryUsage.rss,
          external: memoryUsage.external,
          arrayBuffers: memoryUsage.arrayBuffers,
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
        },
        platform: process.platform,
        nodeVersion: process.version,
        pid: process.pid,
      };

      // Include more detailed info if authenticated
      if (req.user) {
        const detailedSystemInfo = {
          ...systemInfo,
          environment: process.env.NODE_ENV,
          activeSessions: authService.getActiveSessions().length,
          maintenanceStatus: maintenanceService.getStatus(),
        };

        successResponse(res, detailedSystemInfo, 200, requestId);
      } else {
        // Limited info for unauthenticated requests
        const publicSystemInfo = {
          uptime: systemInfo.uptime,
          status: 'operational',
          nodeVersion: systemInfo.nodeVersion,
          platform: systemInfo.platform,
        };

        successResponse(res, publicSystemInfo, 200, requestId);
      }
    } catch (error: any) {
      throw errors.internal('System health check failed');
    }
  })
);

/**
 * POST /api/health/maintenance
 * Trigger database maintenance (requires auth)
 */
router.post('/maintenance',
  optionalAuth, // Require authentication for maintenance operations
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    if (!req.user) {
      throw errors.unauthorized('Authentication required for maintenance operations');
    }

    try {
      const startTime = Date.now();
      await maintenanceService.runFullMaintenance();
      const duration = Date.now() - startTime;

      const maintenanceResult = {
        status: 'completed',
        duration,
        completedAt: new Date().toISOString(),
        operations: ['VACUUM', 'ANALYZE'],
      };

      successResponse(res, maintenanceResult, 200, requestId);
    } catch (error: any) {
      throw errors.internal('Maintenance operation failed');
    }
  })
);

/**
 * POST /api/health/backup
 * Create database backup (requires auth)
 */
router.post('/backup',
  optionalAuth, // Require authentication for backup operations
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    if (!req.user) {
      throw errors.unauthorized('Authentication required for backup operations');
    }

    try {
      const backupPath = await maintenanceService.createBackup();
      
      const backupResult = {
        status: 'completed',
        backupPath,
        createdAt: new Date().toISOString(),
        size: 0, // Could be enhanced to include file size
      };

      successResponse(res, backupResult, 201, requestId);
    } catch (error: any) {
      throw errors.internal('Backup operation failed');
    }
  })
);

/**
 * GET /api/health/metrics
 * Application performance metrics (requires auth)
 */
router.get('/metrics',
  optionalAuth, // Require authentication for detailed metrics
  asyncHandler(async (req: AuthenticatedRequest, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    if (!req.user) {
      throw errors.unauthorized('Authentication required for metrics');
    }

    try {
      const dbStats = await dbService.getStats();
      const maintenanceStats = await maintenanceService.getDatabaseStats();
      const activeSessions = authService.getActiveSessions();

      const metrics = {
        database: {
          totalTasks: dbStats.taskCount,
          completedTasks: dbStats.completedTaskCount,
          pendingTasks: dbStats.taskCount - dbStats.completedTaskCount,
          settings: dbStats.settingsCount,
          size: dbStats.size,
          health: maintenanceStats.health.isHealthy,
        },
        authentication: {
          activeSessions: activeSessions.length,
          sessionDetails: activeSessions.map(session => ({
            tokenId: session.tokenId,
            userId: session.userId,
            createdAt: session.createdAt,
            lastActivity: session.lastActivity,
            ipAddress: session.ipAddress,
          })),
        },
        system: {
          uptime: process.uptime(),
          memoryUsage: process.memoryUsage(),
          cpuUsage: process.cpuUsage(),
        },
        maintenance: {
          status: maintenanceService.getStatus(),
          lastBackup: maintenanceStats.backupInfo.lastBackup,
          backupCount: maintenanceStats.backupInfo.backupCount,
        },
        timestamp: new Date().toISOString(),
      };

      successResponse(res, metrics, 200, requestId);
    } catch (error: any) {
      throw errors.internal('Failed to retrieve metrics');
    }
  })
);

/**
 * GET /api/health/ready
 * Readiness probe for deployment orchestration
 */
router.get('/ready',
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    try {
      // Check if all services are initialized and ready
      const isReady = dbService.initialized;
      
      if (isReady) {
        successResponse(res, {
          status: 'ready',
          timestamp: new Date().toISOString(),
        }, 200, requestId);
      } else {
        throw errors.serviceUnavailable('Service not ready');
      }
    } catch (error: any) {
      throw errors.serviceUnavailable('Service not ready');
    }
  })
);

/**
 * GET /api/health/live
 * Liveness probe for deployment orchestration
 */
router.get('/live',
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.headers['x-request-id'] as string || 'unknown';

    // Simple liveness check - if we can respond, we're alive
    successResponse(res, {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
    }, 200, requestId);
  })
);

export default router;