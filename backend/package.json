{"name": "lifetracker-backend", "version": "1.0.0", "description": "Express.js backend for LifeTracker personal task management application", "main": "dist/app.js", "scripts": {"build": "tsc", "start": "node dist/app.js", "dev": "ts-node src/app.ts", "watch": "tsc --watch", "clean": "rm -rf dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["task-management", "express", "sqlite", "personal-productivity"], "author": "LifeTracker", "license": "MIT", "dependencies": {"bcrypt": "^5.1.1", "better-sqlite3": "^11.6.0", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.4.1", "helmet": "^8.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "uuid": "^11.0.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/better-sqlite3": "^7.6.11", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.10.2", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "jest": "^30.0.5", "supertest": "^7.1.4", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.2"}, "engines": {"node": ">=18.0.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "transform": {"^.+\\.ts$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/**/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}}