/**
 * PM2 Ecosystem Configuration for LifeTracker Backend
 * Used for production deployment and process management
 */

module.exports = {
  apps: [{
    name: 'lifetracker-api',
    script: './dist/app.js',
    cwd: process.cwd(),
    instances: 1, // Single instance for SQLite compatibility
    exec_mode: 'fork', // Fork mode for SQLite
    
    // Environment configuration
    env: {
      NODE_ENV: 'development',
      PORT: 3001,
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001,
    },
    
    // Process management
    max_memory_restart: '500M',
    restart_delay: 5000,
    max_restarts: 10,
    min_uptime: '10s',
    
    // Logging
    log_file: './logs/app.log',
    error_file: './logs/error.log',
    out_file: './logs/out.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // Monitoring
    monitoring: false, // Disable PM2 monitoring for privacy
    
    // Auto-restart configuration
    watch: false, // No file watching in production
    ignore_watch: ['node_modules', 'logs', 'database'],
    
    // Advanced options
    kill_timeout: 5000,
    wait_ready: true,
    listen_timeout: 10000,
    
    // Health check
    health_check_grace_period: 3000,
    
    // Source map support
    source_map_support: true,
    
    // Node.js options
    node_args: '--max-old-space-size=256', // Limit memory usage
  }]
};